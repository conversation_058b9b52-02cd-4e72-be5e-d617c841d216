import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './Navbar.css';

const Navbar = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isActive = (path) => {
    return location.pathname === path ? 'active' : '';
  };

  if (!user) {
    return (
      <nav className="navbar">
        <div className="container">
          <div className="navbar-brand">
            <Link to="/">📝 Todo List</Link>
          </div>
          <div className="navbar-nav">
            <Link to="/login" className={`nav-link ${isActive('/login')}`}>
              Login
            </Link>
            <Link to="/register" className={`nav-link ${isActive('/register')}`}>
              Register
            </Link>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="navbar">
      <div className="container">
        <div className="navbar-brand">
          <Link to="/dashboard">📝 Todo List</Link>
        </div>
        
        <div className={`navbar-nav ${isMenuOpen ? 'active' : ''}`}>
          <Link 
            to="/dashboard" 
            className={`nav-link ${isActive('/dashboard')}`}
            onClick={() => setIsMenuOpen(false)}
          >
            Dashboard
          </Link>
          <Link 
            to="/tasks" 
            className={`nav-link ${isActive('/tasks')}`}
            onClick={() => setIsMenuOpen(false)}
          >
            Tasks
          </Link>
          <Link 
            to="/peers" 
            className={`nav-link ${isActive('/peers')}`}
            onClick={() => setIsMenuOpen(false)}
          >
            Peers
          </Link>
          <Link 
            to="/profile" 
            className={`nav-link ${isActive('/profile')}`}
            onClick={() => setIsMenuOpen(false)}
          >
            Profile
          </Link>
          
          <div className="user-menu">
            <span className="user-name">Hi, {user.name}!</span>
            <div className="efficiency-badge">
              {user.efficiency}% Efficiency
            </div>
            <button onClick={handleLogout} className="btn btn-secondary btn-sm">
              Logout
            </button>
          </div>
        </div>
        
        <button 
          className="mobile-menu-toggle"
          onClick={toggleMenu}
          aria-label="Toggle menu"
        >
          <span></span>
          <span></span>
          <span></span>
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
