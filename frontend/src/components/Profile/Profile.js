import React, { useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import './Profile.css';

const Profile = () => {
  const { user, updateUser } = useAuth();
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    preferences: {
      emailNotifications: user?.preferences?.emailNotifications ?? true,
      locationBasedTasks: user?.preferences?.locationBasedTasks ?? true
    }
  });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await axios.put('/api/users/profile', {
        name: formData.name,
        preferences: formData.preferences
      });

      updateUser(response.data.user);
      toast.success('Profile updated successfully!');
    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(error.response?.data?.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const getEfficiencyLevel = (efficiency) => {
    if (efficiency >= 90) return { level: 'Expert', color: 'gold' };
    if (efficiency >= 75) return { level: 'Advanced', color: 'green' };
    if (efficiency >= 50) return { level: 'Intermediate', color: 'orange' };
    if (efficiency >= 25) return { level: 'Beginner', color: 'blue' };
    return { level: 'Getting Started', color: 'gray' };
  };

  const efficiencyLevel = getEfficiencyLevel(user?.efficiency || 0);

  return (
    <div className="profile-page">
      <div className="container">
        <div className="profile-header">
          <div className="profile-avatar">
            <div className="avatar-large">
              {user?.name?.charAt(0).toUpperCase()}
            </div>
          </div>
          <div className="profile-info">
            <h1>{user?.name}</h1>
            <p className="profile-email">{user?.email}</p>
            <div className="efficiency-badge-large">
              <span className={`efficiency-level ${efficiencyLevel.color}`}>
                {efficiencyLevel.level}
              </span>
              <span className="efficiency-percentage">
                {user?.efficiency || 0}% Efficiency
              </span>
            </div>
          </div>
        </div>

        <div className="profile-tabs">
          <button 
            className={`tab-btn ${activeTab === 'profile' ? 'active' : ''}`}
            onClick={() => setActiveTab('profile')}
          >
            👤 Profile Settings
          </button>
          <button 
            className={`tab-btn ${activeTab === 'stats' ? 'active' : ''}`}
            onClick={() => setActiveTab('stats')}
          >
            📊 Statistics
          </button>
          <button 
            className={`tab-btn ${activeTab === 'preferences' ? 'active' : ''}`}
            onClick={() => setActiveTab('preferences')}
          >
            ⚙️ Preferences
          </button>
        </div>

        <div className="profile-content">
          {activeTab === 'profile' && (
            <div className="tab-content">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Profile Information</h3>
                </div>
                
                <form onSubmit={handleSubmit}>
                  <div className="form-group">
                    <label className="form-label">Full Name</label>
                    <input
                      type="text"
                      name="name"
                      className="form-control"
                      value={formData.name}
                      onChange={handleChange}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Email Address</label>
                    <input
                      type="email"
                      name="email"
                      className="form-control"
                      value={formData.email}
                      disabled
                      title="Email cannot be changed"
                    />
                    <small className="form-text">Email address cannot be changed</small>
                  </div>

                  <div className="form-actions">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <div className="spinner-sm"></div>
                          Updating...
                        </>
                      ) : (
                        'Update Profile'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {activeTab === 'stats' && (
            <div className="tab-content">
              <div className="stats-grid">
                <div className="stat-card">
                  <div className="stat-icon">📋</div>
                  <div className="stat-content">
                    <div className="stat-value">{user?.totalTasks || 0}</div>
                    <div className="stat-label">Total Tasks</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">✅</div>
                  <div className="stat-content">
                    <div className="stat-value">{user?.completedTasks || 0}</div>
                    <div className="stat-label">Completed</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">📈</div>
                  <div className="stat-content">
                    <div className="stat-value">{user?.efficiency || 0}%</div>
                    <div className="stat-label">Efficiency</div>
                  </div>
                </div>

                <div className="stat-card">
                  <div className="stat-icon">🔥</div>
                  <div className="stat-content">
                    <div className="stat-value">{user?.streak || 0}</div>
                    <div className="stat-label">Day Streak</div>
                  </div>
                </div>
              </div>

              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Productivity Insights</h3>
                </div>
                
                <div className="insights-content">
                  <div className="insight-item">
                    <div className="insight-icon">🎯</div>
                    <div className="insight-text">
                      <strong>Efficiency Level:</strong> {efficiencyLevel.level}
                      <p>You're doing great! Keep up the momentum.</p>
                    </div>
                  </div>

                  <div className="insight-item">
                    <div className="insight-icon">📊</div>
                    <div className="insight-text">
                      <strong>Task Completion Rate:</strong> {user?.efficiency || 0}%
                      <p>
                        {user?.efficiency >= 75 
                          ? "Excellent! You're highly productive."
                          : user?.efficiency >= 50
                          ? "Good progress! Try to maintain consistency."
                          : "There's room for improvement. Set smaller, achievable goals."
                        }
                      </p>
                    </div>
                  </div>

                  {user?.streak > 0 && (
                    <div className="insight-item">
                      <div className="insight-icon">🔥</div>
                      <div className="insight-text">
                        <strong>Current Streak:</strong> {user.streak} days
                        <p>Amazing consistency! Don't break the chain.</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="tab-content">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Notification Preferences</h3>
                </div>
                
                <form onSubmit={handleSubmit}>
                  <div className="preference-group">
                    <label className="preference-label">
                      <input
                        type="checkbox"
                        name="preferences.emailNotifications"
                        checked={formData.preferences.emailNotifications}
                        onChange={handleChange}
                      />
                      <div className="preference-content">
                        <strong>📧 Email Notifications</strong>
                        <p>Receive daily task reminders and updates via email</p>
                      </div>
                    </label>
                  </div>

                  <div className="preference-group">
                    <label className="preference-label">
                      <input
                        type="checkbox"
                        name="preferences.locationBasedTasks"
                        checked={formData.preferences.locationBasedTasks}
                        onChange={handleChange}
                      />
                      <div className="preference-content">
                        <strong>📍 Location-Based Tasks</strong>
                        <p>Enable automatic task completion when you reach specified locations</p>
                      </div>
                    </label>
                  </div>

                  <div className="form-actions">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <div className="spinner-sm"></div>
                          Saving...
                        </>
                      ) : (
                        'Save Preferences'
                      )}
                    </button>
                  </div>
                </form>
              </div>

              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">About Location Features</h3>
                </div>
                
                <div className="info-content">
                  <div className="info-item">
                    <div className="info-icon">🗺️</div>
                    <div className="info-text">
                      <strong>How it works:</strong> When you create a location-based task, 
                      the app will track your location and automatically complete the task 
                      when you arrive at the specified location.
                    </div>
                  </div>

                  <div className="info-item">
                    <div className="info-icon">🔒</div>
                    <div className="info-text">
                      <strong>Privacy:</strong> Your location data is only used for task 
                      completion and is not stored or shared with third parties.
                    </div>
                  </div>

                  <div className="info-item">
                    <div className="info-icon">🔋</div>
                    <div className="info-text">
                      <strong>Battery Usage:</strong> Location tracking is optimized to 
                      minimize battery usage while providing accurate results.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Profile;
