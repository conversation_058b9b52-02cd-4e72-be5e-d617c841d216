.profile-page {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.profile-avatar {
  flex-shrink: 0;
}

.avatar-large {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 3px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
}

.profile-info h1 {
  font-size: 2.5rem;
  margin: 0 0 0.5rem 0;
  font-weight: 700;
}

.profile-email {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0 0 1rem 0;
}

.efficiency-badge-large {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.efficiency-level {
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.efficiency-level.gold {
  background: linear-gradient(135deg, #f1c40f, #f39c12);
  color: #333;
}

.efficiency-level.green {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.efficiency-level.orange {
  background: linear-gradient(135deg, #e67e22, #f39c12);
}

.efficiency-level.blue {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.efficiency-level.gray {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.efficiency-percentage {
  font-size: 1.25rem;
  font-weight: 600;
  opacity: 0.9;
}

/* Tabs */
.profile-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #e9ecef;
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.05);
}

.tab-btn.active {
  color: #4CAF50;
  border-bottom-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

/* Tab Content */
.tab-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-text {
  color: #666;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

/* Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  border: 1px solid #e9ecef;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #4CAF50;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  font-weight: 500;
}

/* Insights */
.insights-content {
  padding: 1.5rem;
}

.insight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #4CAF50;
}

.insight-item:last-child {
  margin-bottom: 0;
}

.insight-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.insight-text {
  flex: 1;
}

.insight-text strong {
  color: #333;
  font-size: 1.1rem;
}

.insight-text p {
  margin: 0.5rem 0 0 0;
  color: #666;
  line-height: 1.5;
}

/* Preferences */
.preference-group {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.preference-group:hover {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.02);
}

.preference-label {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  margin: 0;
}

.preference-label input[type="checkbox"] {
  margin-top: 0.25rem;
  transform: scale(1.2);
  accent-color: #4CAF50;
}

.preference-content {
  flex: 1;
}

.preference-content strong {
  display: block;
  color: #333;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.preference-content p {
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* Info Content */
.info-content {
  padding: 1.5rem;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.info-text {
  flex: 1;
  line-height: 1.6;
  color: #666;
}

.info-text strong {
  color: #333;
  font-weight: 600;
}

/* Form Actions */
.form-actions {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .profile-page {
    padding: 1rem 0;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
    padding: 1.5rem;
  }

  .profile-info h1 {
    font-size: 2rem;
  }

  .efficiency-badge-large {
    flex-direction: column;
    gap: 0.5rem;
  }

  .profile-tabs {
    flex-direction: column;
    gap: 0;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
    border-radius: 0;
  }

  .tab-btn.active {
    border-bottom-color: #4CAF50;
    border-left: 3px solid #4CAF50;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1.5rem 1rem;
  }

  .stat-icon {
    font-size: 2rem;
  }

  .stat-value {
    font-size: 2rem;
  }

  .insight-item,
  .info-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .preference-label {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
  }

  .preference-content {
    text-align: center;
  }

  .form-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .avatar-large {
    width: 80px;
    height: 80px;
    font-size: 2rem;
  }

  .profile-info h1 {
    font-size: 1.75rem;
  }
}
