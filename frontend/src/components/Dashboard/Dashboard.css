.dashboard {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-header h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.dashboard-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.dashboard-section {
  min-height: 300px;
}

/* Task Stats Styles */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem 1rem;
  text-align: center;
  transition: transform 0.2s ease;
  border-left: 4px solid #ddd;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-card.blue { border-left-color: #007bff; }
.stat-card.green { border-left-color: #28a745; }
.stat-card.orange { border-left-color: #fd7e14; }
.stat-card.gray { border-left-color: #6c757d; }
.stat-card.red { border-left-color: #dc3545; }

.stat-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 0.25rem;
}

.stat-title {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

.progress-section {
  margin-top: 2rem;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.progress-percentage {
  font-weight: bold;
  color: #4CAF50;
}

.progress-bar {
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.5rem;
}

/* Chart Styles */
.chart-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #666;
}

.stat-item strong {
  color: #4CAF50;
}

.chart-container {
  position: relative;
  height: 200px;
}

.chart {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 100%;
  padding: 1rem 0;
}

.chart-bar-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.chart-bar-wrapper {
  flex: 1;
  display: flex;
  align-items: end;
  width: 100%;
  padding: 0 4px;
}

.chart-bar {
  width: 100%;
  background: linear-gradient(180deg, #4CAF50, #45a049);
  border-radius: 4px 4px 0 0;
  min-height: 4px;
  position: relative;
  display: flex;
  align-items: start;
  justify-content: center;
  transition: all 0.3s ease;
}

.chart-bar:hover {
  opacity: 0.8;
}

.bar-value {
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  padding: 2px 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  margin-top: 4px;
}

.chart-label {
  font-size: 0.75rem;
  color: #666;
  margin-top: 0.5rem;
  text-align: center;
  line-height: 1.2;
}

.empty-chart {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-subtitle {
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Recent Tasks Styles */
.recent-tasks {
  max-height: 400px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 1rem;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.task-content {
  flex: 1;
  margin-right: 1rem;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.task-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.task-badges {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.priority-badge,
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.priority-badge.high,
.status-badge.red { background: #fee; color: #dc3545; }
.priority-badge.medium,
.status-badge.orange { background: #fff3cd; color: #856404; }
.priority-badge.low,
.status-badge.green { background: #d4edda; color: #155724; }
.status-badge.gray { background: #f8f9fa; color: #6c757d; }

.task-description {
  font-size: 0.875rem;
  color: #666;
  margin: 0.5rem 0;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: #666;
}

.location-badge {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.task-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Leaderboard Styles */
.leaderboard-list {
  max-height: 400px;
  overflow-y: auto;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 0.5rem;
  background: #f8f9fa;
  transition: all 0.2s ease;
}

.leaderboard-item:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.leaderboard-item.current-user {
  background: #e8f5e8;
  border-color: #4CAF50;
}

.rank {
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1rem;
  min-width: 3rem;
  text-align: center;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.you-badge {
  background: #4CAF50;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

.user-stats {
  font-size: 0.875rem;
  color: #666;
  margin-top: 0.25rem;
  display: flex;
  gap: 1rem;
}

.streak {
  color: #ff6b35;
  font-weight: 500;
}

.efficiency-score {
  margin-left: 1rem;
}

.efficiency-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 0.875rem;
}

.efficiency-circle.green { background: #28a745; }
.efficiency-circle.orange { background: #fd7e14; }
.efficiency-circle.yellow { background: #ffc107; color: #333; }
.efficiency-circle.red { background: #dc3545; }

/* Quick Actions */
.quick-actions {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
  margin-bottom: 1.5rem;
  color: #333;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.empty-state .empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-state p {
  margin: 0.5rem 0;
}

.empty-state .empty-subtitle {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboard {
    padding: 1rem 0;
  }

  .dashboard-header h1 {
    font-size: 2rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .task-item {
    flex-direction: column;
    align-items: stretch;
  }

  .task-content {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .task-actions {
    flex-direction: row;
    justify-content: flex-end;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons .btn {
    width: 200px;
  }
}
