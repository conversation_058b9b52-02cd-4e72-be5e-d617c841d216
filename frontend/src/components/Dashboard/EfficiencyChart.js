import React from 'react';

const EfficiencyChart = ({ data }) => {
  const maxValue = Math.max(...data.map(d => d.completed), 1);
  
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const getBarHeight = (value) => {
    return (value / maxValue) * 100;
  };

  const totalCompleted = data.reduce((sum, day) => sum + day.completed, 0);
  const averagePerDay = (totalCompleted / data.length).toFixed(1);

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">7-Day Efficiency Trend</h3>
        <div className="chart-stats">
          <span className="stat-item">
            <strong>{totalCompleted}</strong> tasks completed
          </span>
          <span className="stat-item">
            <strong>{averagePerDay}</strong> avg/day
          </span>
        </div>
      </div>
      
      <div className="chart-container">
        <div className="chart">
          {data.map((day, index) => (
            <div key={index} className="chart-bar-container">
              <div className="chart-bar-wrapper">
                <div 
                  className="chart-bar"
                  style={{ height: `${getBarHeight(day.completed)}%` }}
                  title={`${day.completed} tasks completed`}
                >
                  {day.completed > 0 && (
                    <span className="bar-value">{day.completed}</span>
                  )}
                </div>
              </div>
              <div className="chart-label">
                {formatDate(day.date)}
              </div>
            </div>
          ))}
        </div>
        
        {totalCompleted === 0 && (
          <div className="empty-chart">
            <div className="empty-icon">📊</div>
            <p>No completed tasks in the last 7 days</p>
            <p className="empty-subtitle">Start completing tasks to see your progress!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EfficiencyChart;
