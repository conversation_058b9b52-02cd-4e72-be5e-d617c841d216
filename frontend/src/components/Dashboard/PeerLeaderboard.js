import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const PeerLeaderboard = () => {
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchLeaderboard();
  }, []);

  const fetchLeaderboard = async () => {
    try {
      const response = await axios.get('/api/peers/leaderboard');
      setLeaderboard(response.data.leaderboard);
    } catch (error) {
      console.error('Leaderboard fetch error:', error);
      if (error.response?.status !== 404) {
        toast.error('Failed to load leaderboard');
      }
    } finally {
      setLoading(false);
    }
  };

  const getRankIcon = (index) => {
    switch (index) {
      case 0: return '🥇';
      case 1: return '🥈';
      case 2: return '🥉';
      default: return `#${index + 1}`;
    }
  };

  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 80) return 'green';
    if (efficiency >= 60) return 'orange';
    if (efficiency >= 40) return 'yellow';
    return 'red';
  };

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Peer Leaderboard</h3>
        </div>
        <div className="loading">
          <div className="spinner"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Peer Leaderboard</h3>
        <button 
          className="btn btn-secondary btn-sm"
          onClick={() => window.location.href = '/peers'}
        >
          Manage Peers
        </button>
      </div>
      
      <div className="leaderboard">
        {leaderboard.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">👥</div>
            <p>No peers added yet</p>
            <p className="empty-subtitle">Invite peers to see the leaderboard!</p>
            <button 
              className="btn btn-primary"
              onClick={() => window.location.href = '/peers'}
            >
              Invite Peers
            </button>
          </div>
        ) : (
          <div className="leaderboard-list">
            {leaderboard.map((user, index) => (
              <div 
                key={user.id} 
                className={`leaderboard-item ${user.isCurrentUser ? 'current-user' : ''}`}
              >
                <div className="rank">
                  {getRankIcon(index)}
                </div>
                
                <div className="user-info">
                  <div className="user-name">
                    {user.name}
                    {user.isCurrentUser && <span className="you-badge">You</span>}
                  </div>
                  <div className="user-stats">
                    <span>{user.completedTasks}/{user.totalTasks} tasks</span>
                    {user.streak > 0 && (
                      <span className="streak">🔥 {user.streak} day streak</span>
                    )}
                  </div>
                </div>
                
                <div className="efficiency-score">
                  <div className={`efficiency-circle ${getEfficiencyColor(user.efficiency)}`}>
                    {user.efficiency}%
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PeerLeaderboard;
