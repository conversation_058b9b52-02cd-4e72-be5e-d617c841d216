import React from 'react';

const TaskStats = ({ stats }) => {
  const getProgressPercentage = () => {
    if (stats.total === 0) return 0;
    return Math.round((stats.completed / stats.total) * 100);
  };

  const statCards = [
    {
      title: 'Total Tasks',
      value: stats.total,
      icon: '📋',
      color: 'blue'
    },
    {
      title: 'Completed',
      value: stats.completed,
      icon: '✅',
      color: 'green'
    },
    {
      title: 'In Progress',
      value: stats.inProgress,
      icon: '⏳',
      color: 'orange'
    },
    {
      title: 'Pending',
      value: stats.pending,
      icon: '⏸️',
      color: 'gray'
    },
    {
      title: 'Overdue',
      value: stats.overdue,
      icon: '⚠️',
      color: 'red'
    }
  ];

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Task Overview</h3>
      </div>
      
      <div className="stats-grid">
        {statCards.map((stat, index) => (
          <div key={index} className={`stat-card ${stat.color}`}>
            <div className="stat-icon">{stat.icon}</div>
            <div className="stat-content">
              <div className="stat-value">{stat.value}</div>
              <div className="stat-title">{stat.title}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Progress Bar */}
      <div className="progress-section">
        <div className="progress-header">
          <span>Overall Progress</span>
          <span className="progress-percentage">{getProgressPercentage()}%</span>
        </div>
        <div className="progress-bar">
          <div 
            className="progress-fill"
            style={{ width: `${getProgressPercentage()}%` }}
          ></div>
        </div>
        <div className="progress-text">
          {stats.completed} of {stats.total} tasks completed
        </div>
      </div>
    </div>
  );
};

export default TaskStats;
