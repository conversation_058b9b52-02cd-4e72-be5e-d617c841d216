import React from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const RecentTasks = ({ tasks, onTaskUpdate }) => {
  const handleStatusChange = async (taskId, newStatus) => {
    try {
      await axios.put(`/api/tasks/${taskId}`, { status: newStatus });
      toast.success('Task updated successfully');
      onTaskUpdate();
    } catch (error) {
      console.error('Update task error:', error);
      toast.error('Failed to update task');
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'gray';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'green';
      case 'in-progress': return 'orange';
      case 'pending': return 'gray';
      default: return 'gray';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No due date';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else {
      return `Due in ${diffDays} days`;
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">Recent Tasks</h3>
        <button 
          className="btn btn-primary btn-sm"
          onClick={() => window.location.href = '/tasks'}
        >
          View All
        </button>
      </div>
      
      <div className="recent-tasks">
        {tasks.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📝</div>
            <p>No tasks yet</p>
            <p className="empty-subtitle">Create your first task to get started!</p>
            <button 
              className="btn btn-primary"
              onClick={() => window.location.href = '/tasks'}
            >
              Create Task
            </button>
          </div>
        ) : (
          tasks.map((task) => (
            <div key={task._id} className="task-item">
              <div className="task-content">
                <div className="task-header">
                  <h4 className="task-title">{task.title}</h4>
                  <div className="task-badges">
                    <span className={`priority-badge ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </span>
                    <span className={`status-badge ${getStatusColor(task.status)}`}>
                      {task.status}
                    </span>
                  </div>
                </div>
                
                {task.description && (
                  <p className="task-description">{task.description}</p>
                )}
                
                <div className="task-meta">
                  <span className="task-date">
                    {formatDate(task.dueDate)}
                  </span>
                  {task.isLocationBased && (
                    <span className="location-badge">
                      📍 Location-based
                    </span>
                  )}
                </div>
              </div>
              
              <div className="task-actions">
                {task.status !== 'completed' && (
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => handleStatusChange(task._id, 'completed')}
                  >
                    ✓ Complete
                  </button>
                )}
                {task.status === 'pending' && (
                  <button
                    className="btn btn-warning btn-sm"
                    onClick={() => handleStatusChange(task._id, 'in-progress')}
                  >
                    ▶ Start
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default RecentTasks;
