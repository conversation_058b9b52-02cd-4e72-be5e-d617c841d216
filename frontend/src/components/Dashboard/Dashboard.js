import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import { useAuth } from '../../contexts/AuthContext';
import TaskStats from './TaskStats';
import EfficiencyChart from './EfficiencyChart';
import RecentTasks from './RecentTasks';
import PeerLeaderboard from './PeerLeaderboard';
import './Dashboard.css';

const Dashboard = () => {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/api/users/dashboard');
      setDashboardData(response.data.dashboard);
    } catch (error) {
      console.error('Dashboard fetch error:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="container">
        <div className="alert alert-error">
          Failed to load dashboard data. Please try refreshing the page.
        </div>
      </div>
    );
  }

  const { taskStats, recentTasks, efficiencyTrend } = dashboardData;

  return (
    <div className="dashboard">
      <div className="container">
        <div className="dashboard-header">
          <h1>Welcome back, {user.name}! 👋</h1>
          <p className="dashboard-subtitle">
            Here's what's happening with your tasks today
          </p>
        </div>

        <div className="dashboard-grid">
          {/* Task Statistics */}
          <div className="dashboard-section">
            <TaskStats stats={taskStats} />
          </div>

          {/* Efficiency Chart */}
          <div className="dashboard-section">
            <EfficiencyChart data={efficiencyTrend} />
          </div>

          {/* Recent Tasks */}
          <div className="dashboard-section">
            <RecentTasks 
              tasks={recentTasks} 
              onTaskUpdate={fetchDashboardData}
            />
          </div>

          {/* Peer Leaderboard */}
          <div className="dashboard-section">
            <PeerLeaderboard />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="quick-actions">
          <h3>Quick Actions</h3>
          <div className="action-buttons">
            <button 
              className="btn btn-primary"
              onClick={() => window.location.href = '/tasks'}
            >
              📝 Create New Task
            </button>
            <button 
              className="btn btn-secondary"
              onClick={() => window.location.href = '/peers'}
            >
              👥 Invite Peers
            </button>
            <button 
              className="btn btn-warning"
              onClick={() => {
                if (navigator.geolocation) {
                  navigator.geolocation.getCurrentPosition(
                    (position) => {
                      toast.success(`Location: ${position.coords.latitude}, ${position.coords.longitude}`);
                    },
                    (error) => {
                      toast.error('Unable to get location');
                    }
                  );
                } else {
                  toast.error('Geolocation is not supported');
                }
              }}
            >
              📍 Check Location
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
