.peers-page {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.peers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.peers-header h1 {
  color: #333;
  font-size: 2rem;
  margin: 0;
}

.peers-content {
  display: grid;
  gap: 2rem;
}

.peers-section {
  width: 100%;
}

/* Peer Invite Form */
.peer-invite-form {
  padding: 2rem;
  max-width: 500px;
  width: 100%;
}

.invite-content {
  margin-top: 1rem;
}

.invite-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #4CAF50;
}

.search-input-container {
  position: relative;
}

.search-spinner {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  max-height: 200px;
  overflow-y: auto;
}

.search-results-header {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-bottom: 1px solid #eee;
}

.search-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-item:hover {
  background: #f8f9fa;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-email {
  font-size: 0.875rem;
  color: #666;
}

.select-btn {
  background: none;
  border: 1px solid #4CAF50;
  color: #4CAF50;
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.select-btn:hover {
  background: #4CAF50;
  color: white;
}

.invite-features {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.invite-features h4 {
  color: #333;
  margin-bottom: 1rem;
}

.invite-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.invite-features li {
  padding: 0.5rem 0;
  color: #666;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Peer Item */
.peers-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.peer-item {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.peer-item:hover {
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.peer-avatar {
  margin-right: 1rem;
}

.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
}

.peer-info {
  flex: 1;
}

.peer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.peer-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.peer-badges {
  display: flex;
  gap: 0.5rem;
}

.streak-badge {
  background: #ff6b35;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.peer-email {
  color: #666;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
}

.peer-stats {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 0.5rem;
}

.stat-group {
  display: flex;
  gap: 0.25rem;
  font-size: 0.875rem;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 500;
  color: #333;
}

.stat-value.efficiency-green { color: #28a745; }
.stat-value.efficiency-orange { color: #fd7e14; }
.stat-value.efficiency-yellow { color: #ffc107; }
.stat-value.efficiency-red { color: #dc3545; }

.peer-meta {
  margin-top: 0.5rem;
}

.added-date {
  color: #999;
  font-size: 0.75rem;
}

.peer-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.efficiency-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  font-size: 0.875rem;
}

.efficiency-circle.green { background: #28a745; }
.efficiency-circle.orange { background: #fd7e14; }
.efficiency-circle.yellow { background: #ffc107; color: #333; }
.efficiency-circle.red { background: #dc3545; }

/* Leaderboard */
.leaderboard-stats {
  color: #666;
  font-size: 0.875rem;
}

.leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.leaderboard-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;
}

.leaderboard-item:hover {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.leaderboard-item.current-user {
  background: #e8f5e8;
  border-color: #4CAF50;
}

.leaderboard-item.gold {
  background: linear-gradient(135deg, #fff9c4, #f7e98e);
  border-color: #f1c40f;
}

.leaderboard-item.silver {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-color: #95a5a6;
}

.leaderboard-item.bronze {
  background: linear-gradient(135deg, #fdf2e9, #f39c12);
  border-color: #e67e22;
}

.rank {
  font-size: 1.5rem;
  font-weight: bold;
  margin-right: 1rem;
  min-width: 3rem;
  text-align: center;
}

.rank.rank-gold { color: #f1c40f; }
.rank.rank-silver { color: #95a5a6; }
.rank.rank-bronze { color: #e67e22; }

.user-avatar {
  margin-right: 1rem;
}

.leaderboard-item .avatar-circle {
  width: 40px;
  height: 40px;
  font-size: 1rem;
}

.user-name {
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.you-badge {
  background: #4CAF50;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.user-stats {
  font-size: 0.875rem;
  color: #666;
  display: flex;
  gap: 1rem;
}

.task-count {
  color: #333;
}

.streak {
  color: #ff6b35;
  font-weight: 500;
}

.efficiency-score {
  margin-left: auto;
  text-align: center;
}

.leaderboard-item .efficiency-circle {
  width: 50px;
  height: 50px;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.efficiency-label {
  font-size: 0.75rem;
  color: #666;
}

.leaderboard-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.achievement-info {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1rem;
}

.achievement-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #666;
}

.achievement-icon {
  font-size: 1rem;
}

/* Peer Statistics */
.peer-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-item .stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #4CAF50;
  display: block;
  margin-bottom: 0.5rem;
}

.stat-item .stat-label {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
}

/* Tips Card */
.tips-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.tips-card .card-title {
  color: white;
}

.tips-content {
  padding: 1rem 0;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.tip-text {
  line-height: 1.5;
}

.tip-text strong {
  color: #fff;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .peers-page {
    padding: 1rem 0;
  }

  .peers-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .peer-invite-form {
    padding: 1.5rem;
  }

  .peer-item {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
    gap: 1rem;
  }

  .peer-header {
    justify-content: center;
  }

  .peer-stats {
    justify-content: center;
  }

  .peer-actions {
    justify-content: center;
  }

  .leaderboard-item {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .efficiency-score {
    margin-left: 0;
  }

  .achievement-info {
    flex-direction: column;
    align-items: center;
  }

  .peer-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .tip-item {
    flex-direction: column;
    text-align: center;
  }
}
