import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import PeerInviteForm from './PeerInviteForm';
import PeerItem from './PeerItem';
import PeerLeaderboard from './PeerLeaderboard';
import './Peers.css';

const PeerList = () => {
  const [peers, setPeers] = useState([]);
  const [leaderboard, setLeaderboard] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showInviteForm, setShowInviteForm] = useState(false);

  useEffect(() => {
    fetchPeers();
    fetchLeaderboard();
  }, []);

  const fetchPeers = async () => {
    try {
      const response = await axios.get('/api/peers');
      setPeers(response.data.peers);
    } catch (error) {
      console.error('Fetch peers error:', error);
      toast.error('Failed to load peers');
    }
  };

  const fetchLeaderboard = async () => {
    try {
      const response = await axios.get('/api/peers/leaderboard');
      setLeaderboard(response.data.leaderboard);
    } catch (error) {
      console.error('Fetch leaderboard error:', error);
      if (error.response?.status !== 404) {
        toast.error('Failed to load leaderboard');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInvitePeer = async (email) => {
    try {
      await axios.post('/api/peers/invite', { email });
      toast.success('Peer invitation sent successfully!');
      setShowInviteForm(false);
      fetchPeers();
      fetchLeaderboard();
    } catch (error) {
      console.error('Invite peer error:', error);
      toast.error(error.response?.data?.message || 'Failed to invite peer');
    }
  };

  const handleRemovePeer = async (peerId) => {
    if (!window.confirm('Are you sure you want to remove this peer?')) {
      return;
    }

    try {
      await axios.delete(`/api/peers/${peerId}`);
      toast.success('Peer removed successfully');
      fetchPeers();
      fetchLeaderboard();
    } catch (error) {
      console.error('Remove peer error:', error);
      toast.error('Failed to remove peer');
    }
  };

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="peers-page">
      <div className="container">
        <div className="peers-header">
          <h1>My Peers</h1>
          <button 
            className="btn btn-primary"
            onClick={() => setShowInviteForm(true)}
          >
            👥 Invite Peer
          </button>
        </div>

        {/* Invite Form Modal */}
        {showInviteForm && (
          <div className="modal-overlay">
            <div className="modal">
              <PeerInviteForm
                onInvite={handleInvitePeer}
                onCancel={() => setShowInviteForm(false)}
              />
            </div>
          </div>
        )}

        <div className="peers-content">
          {/* Leaderboard Section */}
          <div className="peers-section">
            <PeerLeaderboard 
              leaderboard={leaderboard}
              showTitle={true}
            />
          </div>

          {/* Peers List Section */}
          <div className="peers-section">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">Your Peers ({peers.length})</h3>
              </div>
              
              <div className="peers-list">
                {peers.length === 0 ? (
                  <div className="empty-state">
                    <div className="empty-icon">👥</div>
                    <h3>No peers added yet</h3>
                    <p>Invite your friends and colleagues to track productivity together!</p>
                    <button 
                      className="btn btn-primary"
                      onClick={() => setShowInviteForm(true)}
                    >
                      Invite Your First Peer
                    </button>
                  </div>
                ) : (
                  peers.map(peer => (
                    <PeerItem
                      key={peer.id}
                      peer={peer}
                      onRemove={handleRemovePeer}
                    />
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Statistics Section */}
          {peers.length > 0 && (
            <div className="peers-section">
              <div className="card">
                <div className="card-header">
                  <h3 className="card-title">Peer Statistics</h3>
                </div>
                
                <div className="peer-stats">
                  <div className="stat-item">
                    <div className="stat-value">{peers.length}</div>
                    <div className="stat-label">Total Peers</div>
                  </div>
                  
                  <div className="stat-item">
                    <div className="stat-value">
                      {Math.round(peers.reduce((sum, peer) => sum + peer.efficiency, 0) / peers.length)}%
                    </div>
                    <div className="stat-label">Average Efficiency</div>
                  </div>
                  
                  <div className="stat-item">
                    <div className="stat-value">
                      {peers.reduce((sum, peer) => sum + peer.completedTasks, 0)}
                    </div>
                    <div className="stat-label">Total Completed Tasks</div>
                  </div>
                  
                  <div className="stat-item">
                    <div className="stat-value">
                      {Math.max(...peers.map(peer => peer.streak), 0)}
                    </div>
                    <div className="stat-label">Highest Streak</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Tips Section */}
          <div className="peers-section">
            <div className="card tips-card">
              <div className="card-header">
                <h3 className="card-title">💡 Peer Productivity Tips</h3>
              </div>
              
              <div className="tips-content">
                <div className="tip-item">
                  <div className="tip-icon">🏆</div>
                  <div className="tip-text">
                    <strong>Friendly Competition:</strong> Use the leaderboard to motivate each other and celebrate achievements.
                  </div>
                </div>
                
                <div className="tip-item">
                  <div className="tip-icon">📊</div>
                  <div className="tip-text">
                    <strong>Track Progress:</strong> Monitor your peers' efficiency to learn from their productivity habits.
                  </div>
                </div>
                
                <div className="tip-item">
                  <div className="tip-icon">🤝</div>
                  <div className="tip-text">
                    <strong>Collaborate:</strong> Share tasks and work together on common goals for better results.
                  </div>
                </div>
                
                <div className="tip-item">
                  <div className="tip-icon">🔥</div>
                  <div className="tip-text">
                    <strong>Maintain Streaks:</strong> Encourage each other to maintain daily task completion streaks.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PeerList;
