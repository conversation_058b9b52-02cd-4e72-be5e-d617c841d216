import React, { useState } from 'react';
import axios from 'axios';

const PeerInviteForm = ({ onInvite, onCancel }) => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);

  const handleEmailChange = async (e) => {
    const value = e.target.value;
    setEmail(value);

    // Search for users as they type
    if (value.length > 2 && value.includes('@')) {
      setSearching(true);
      try {
        const response = await axios.get(`/api/users/search?email=${encodeURIComponent(value)}`);
        setSearchResults(response.data.users);
      } catch (error) {
        console.error('Search users error:', error);
        setSearchResults([]);
      } finally {
        setSearching(false);
      }
    } else {
      setSearchResults([]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email.trim()) return;

    setLoading(true);
    try {
      await onInvite(email.trim());
      setEmail('');
      setSearchResults([]);
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setLoading(false);
    }
  };

  const selectUser = (user) => {
    setEmail(user.email);
    setSearchResults([]);
  };

  return (
    <div className="peer-invite-form">
      <div className="form-header">
        <h2>Invite a Peer</h2>
        <button className="close-btn" onClick={onCancel}>×</button>
      </div>

      <div className="invite-content">
        <p className="invite-description">
          Invite your friends, colleagues, or family members to join your productivity network. 
          You'll be able to track each other's efficiency and motivate one another!
        </p>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label className="form-label">Email Address</label>
            <div className="search-input-container">
              <input
                type="email"
                className="form-control"
                value={email}
                onChange={handleEmailChange}
                placeholder="Enter email address"
                required
              />
              {searching && (
                <div className="search-spinner">
                  <div className="spinner-sm"></div>
                </div>
              )}
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="search-results">
                <div className="search-results-header">
                  <small>Found users:</small>
                </div>
                {searchResults.map(user => (
                  <div 
                    key={user._id} 
                    className="search-result-item"
                    onClick={() => selectUser(user)}
                  >
                    <div className="user-info">
                      <strong>{user.name}</strong>
                      <span className="user-email">{user.email}</span>
                    </div>
                    <button type="button" className="select-btn">
                      Select
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="invite-features">
            <h4>What happens when you invite a peer?</h4>
            <ul>
              <li>✅ They'll receive an email invitation</li>
              <li>📊 You can view each other's efficiency stats</li>
              <li>🏆 Compete on the leaderboard</li>
              <li>🤝 Collaborate on tasks and goals</li>
              <li>📧 Get motivated through friendly competition</li>
            </ul>
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || !email.trim()}
            >
              {loading ? (
                <>
                  <div className="spinner-sm"></div>
                  Sending Invitation...
                </>
              ) : (
                '📧 Send Invitation'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PeerInviteForm;
