import React from 'react';

const PeerLeaderboard = ({ leaderboard, showTitle = false }) => {
  const getRankIcon = (index) => {
    switch (index) {
      case 0: return '🥇';
      case 1: return '🥈';
      case 2: return '🥉';
      default: return `#${index + 1}`;
    }
  };

  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 80) return 'green';
    if (efficiency >= 60) return 'orange';
    if (efficiency >= 40) return 'yellow';
    return 'red';
  };

  const getRankClass = (index) => {
    if (index === 0) return 'gold';
    if (index === 1) return 'silver';
    if (index === 2) return 'bronze';
    return '';
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="card-title">
          {showTitle ? '🏆 Efficiency Leaderboard' : 'Leaderboard'}
        </h3>
        {leaderboard.length > 0 && (
          <div className="leaderboard-stats">
            <small>{leaderboard.length} participant{leaderboard.length !== 1 ? 's' : ''}</small>
          </div>
        )}
      </div>
      
      <div className="leaderboard">
        {leaderboard.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">🏆</div>
            <p>No leaderboard data yet</p>
            <p className="empty-subtitle">Invite peers to see the competition!</p>
          </div>
        ) : (
          <div className="leaderboard-list">
            {leaderboard.map((user, index) => (
              <div 
                key={user.id} 
                className={`leaderboard-item ${user.isCurrentUser ? 'current-user' : ''} ${getRankClass(index)}`}
              >
                <div className={`rank rank-${getRankClass(index)}`}>
                  {getRankIcon(index)}
                </div>
                
                <div className="user-avatar">
                  <div className="avatar-circle">
                    {user.name.charAt(0).toUpperCase()}
                  </div>
                </div>
                
                <div className="user-info">
                  <div className="user-name">
                    {user.name}
                    {user.isCurrentUser && <span className="you-badge">You</span>}
                  </div>
                  <div className="user-stats">
                    <span className="task-count">
                      {user.completedTasks}/{user.totalTasks} tasks
                    </span>
                    {user.streak > 0 && (
                      <span className="streak">
                        🔥 {user.streak} day{user.streak !== 1 ? 's' : ''}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="efficiency-score">
                  <div className={`efficiency-circle ${getEfficiencyColor(user.efficiency)}`}>
                    {user.efficiency}%
                  </div>
                  <div className="efficiency-label">
                    Efficiency
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {leaderboard.length > 0 && (
        <div className="leaderboard-footer">
          <div className="achievement-info">
            <div className="achievement-item">
              <span className="achievement-icon">🎯</span>
              <span className="achievement-text">
                Top performer: {leaderboard[0]?.efficiency}% efficiency
              </span>
            </div>
            
            {leaderboard.some(user => user.streak > 0) && (
              <div className="achievement-item">
                <span className="achievement-icon">🔥</span>
                <span className="achievement-text">
                  Longest streak: {Math.max(...leaderboard.map(u => u.streak))} days
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default PeerLeaderboard;
