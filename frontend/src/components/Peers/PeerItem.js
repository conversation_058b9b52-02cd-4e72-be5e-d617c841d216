import React from 'react';

const PeerItem = ({ peer, onRemove }) => {
  const getEfficiencyColor = (efficiency) => {
    if (efficiency >= 80) return 'green';
    if (efficiency >= 60) return 'orange';
    if (efficiency >= 40) return 'yellow';
    return 'red';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now - date;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return 'Added today';
    } else if (diffDays === 1) {
      return 'Added yesterday';
    } else if (diffDays < 30) {
      return `Added ${diffDays} days ago`;
    } else {
      return `Added on ${date.toLocaleDateString()}`;
    }
  };

  return (
    <div className="peer-item">
      <div className="peer-avatar">
        <div className="avatar-circle">
          {peer.name.charAt(0).toUpperCase()}
        </div>
      </div>

      <div className="peer-info">
        <div className="peer-header">
          <h4 className="peer-name">{peer.name}</h4>
          <div className="peer-badges">
            {peer.streak > 0 && (
              <span className="streak-badge">
                🔥 {peer.streak} day{peer.streak !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>

        <div className="peer-email">{peer.email}</div>

        <div className="peer-stats">
          <div className="stat-group">
            <span className="stat-label">Tasks:</span>
            <span className="stat-value">
              {peer.completedTasks}/{peer.totalTasks}
            </span>
          </div>
          
          <div className="stat-group">
            <span className="stat-label">Efficiency:</span>
            <span className={`stat-value efficiency-${getEfficiencyColor(peer.efficiency)}`}>
              {peer.efficiency}%
            </span>
          </div>
        </div>

        <div className="peer-meta">
          <small className="added-date">{formatDate(peer.addedAt)}</small>
        </div>
      </div>

      <div className="peer-actions">
        <div className={`efficiency-circle ${getEfficiencyColor(peer.efficiency)}`}>
          {peer.efficiency}%
        </div>
        
        <button
          className="btn btn-danger btn-sm"
          onClick={() => onRemove(peer.id)}
          title="Remove peer"
        >
          🗑️
        </button>
      </div>
    </div>
  );
};

export default PeerItem;
