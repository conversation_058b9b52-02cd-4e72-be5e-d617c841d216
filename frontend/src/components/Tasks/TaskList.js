import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import TaskForm from './TaskForm';
import TaskItem from './TaskItem';
import LocationTaskHandler from './LocationTaskHandler';
import './Tasks.css';

const TaskList = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  useEffect(() => {
    fetchTasks();
  }, [filters, pagination.page]);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      });

      const response = await axios.get(`/api/tasks?${params}`);
      setTasks(response.data.tasks);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Fetch tasks error:', error);
      toast.error('Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTask = () => {
    setEditingTask(null);
    setShowForm(true);
  };

  const handleEditTask = (task) => {
    setEditingTask(task);
    setShowForm(true);
  };

  const handleDeleteTask = async (taskId) => {
    if (!window.confirm('Are you sure you want to delete this task?')) {
      return;
    }

    try {
      await axios.delete(`/api/tasks/${taskId}`);
      toast.success('Task deleted successfully');
      fetchTasks();
    } catch (error) {
      console.error('Delete task error:', error);
      toast.error('Failed to delete task');
    }
  };

  const handleTaskUpdate = () => {
    fetchTasks();
    setShowForm(false);
    setEditingTask(null);
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const filteredTasks = tasks.filter(task => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return task.title.toLowerCase().includes(searchLower) ||
             (task.description && task.description.toLowerCase().includes(searchLower));
    }
    return true;
  });

  return (
    <div className="tasks-page">
      <div className="container">
        <div className="tasks-header">
          <h1>My Tasks</h1>
          <button 
            className="btn btn-primary"
            onClick={handleCreateTask}
          >
            ➕ Create Task
          </button>
        </div>

        {/* Filters */}
        <div className="tasks-filters">
          <div className="filter-group">
            <input
              type="text"
              placeholder="Search tasks..."
              className="form-control"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
          
          <div className="filter-group">
            <select
              className="form-control"
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
            </select>
          </div>

          <div className="filter-group">
            <select
              className="form-control"
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
            >
              <option value="">All Priority</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
        </div>

        {/* Task Form Modal */}
        {showForm && (
          <div className="modal-overlay">
            <div className="modal">
              <TaskForm
                task={editingTask}
                onSave={handleTaskUpdate}
                onCancel={() => setShowForm(false)}
              />
            </div>
          </div>
        )}

        {/* Location Task Handler */}
        <LocationTaskHandler tasks={filteredTasks} onTaskUpdate={fetchTasks} />

        {/* Tasks List */}
        <div className="tasks-list">
          {loading ? (
            <div className="loading">
              <div className="spinner"></div>
            </div>
          ) : filteredTasks.length === 0 ? (
            <div className="empty-state">
              <div className="empty-icon">📝</div>
              <h3>No tasks found</h3>
              <p>Create your first task to get started!</p>
              <button 
                className="btn btn-primary"
                onClick={handleCreateTask}
              >
                Create Task
              </button>
            </div>
          ) : (
            <>
              {filteredTasks.map(task => (
                <TaskItem
                  key={task._id}
                  task={task}
                  onEdit={handleEditTask}
                  onDelete={handleDeleteTask}
                  onUpdate={fetchTasks}
                />
              ))}

              {/* Pagination */}
              {pagination.pages > 1 && (
                <div className="pagination">
                  <button
                    className="btn btn-secondary"
                    disabled={pagination.page === 1}
                    onClick={() => handlePageChange(pagination.page - 1)}
                  >
                    Previous
                  </button>
                  
                  <span className="pagination-info">
                    Page {pagination.page} of {pagination.pages}
                  </span>
                  
                  <button
                    className="btn btn-secondary"
                    disabled={pagination.page === pagination.pages}
                    onClick={() => handlePageChange(pagination.page + 1)}
                  >
                    Next
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default TaskList;
