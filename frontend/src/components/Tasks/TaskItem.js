import React, { useState } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const TaskItem = ({ task, onEdit, onDelete, onUpdate }) => {
  const [loading, setLoading] = useState(false);

  const handleStatusChange = async (newStatus) => {
    setLoading(true);
    try {
      await axios.put(`/api/tasks/${task._id}`, { status: newStatus });
      toast.success('Task updated successfully');
      onUpdate();
    } catch (error) {
      console.error('Update task error:', error);
      toast.error('Failed to update task');
    } finally {
      setLoading(false);
    }
  };

  const handleLocationComplete = async () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported');
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          setLoading(true);
          await axios.post(`/api/tasks/${task._id}/complete-by-location`, {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
          toast.success('Task completed by location!');
          onUpdate();
        } catch (error) {
          console.error('Location completion error:', error);
          toast.error(error.response?.data?.message || 'Failed to complete task by location');
        } finally {
          setLoading(false);
        }
      },
      (error) => {
        toast.error('Unable to get current location');
        console.error('Geolocation error:', error);
      }
    );
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'red';
      case 'medium': return 'orange';
      case 'low': return 'green';
      default: return 'gray';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'green';
      case 'in-progress': return 'orange';
      case 'pending': return 'gray';
      default: return 'gray';
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'No due date';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = date - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `Overdue by ${Math.abs(diffDays)} day${Math.abs(diffDays) !== 1 ? 's' : ''}`;
    } else if (diffDays === 0) {
      return 'Due today';
    } else if (diffDays === 1) {
      return 'Due tomorrow';
    } else {
      return `Due in ${diffDays} days`;
    }
  };

  const isOverdue = () => {
    if (!task.dueDate) return false;
    return new Date(task.dueDate) < new Date() && task.status !== 'completed';
  };

  return (
    <div className={`task-item ${task.status} ${isOverdue() ? 'overdue' : ''}`}>
      <div className="task-content">
        <div className="task-header">
          <h3 className="task-title">{task.title}</h3>
          <div className="task-badges">
            <span className={`priority-badge ${getPriorityColor(task.priority)}`}>
              {task.priority}
            </span>
            <span className={`status-badge ${getStatusColor(task.status)}`}>
              {task.status}
            </span>
            {task.isLocationBased && (
              <span className="location-badge">
                📍 Location
              </span>
            )}
          </div>
        </div>

        {task.description && (
          <p className="task-description">{task.description}</p>
        )}

        <div className="task-meta">
          <span className={`task-date ${isOverdue() ? 'overdue-text' : ''}`}>
            {formatDate(task.dueDate)}
          </span>
          
          {task.isLocationBased && task.location.name && (
            <span className="location-info">
              📍 {task.location.name}
            </span>
          )}

          {task.completedAt && (
            <span className="completed-info">
              ✅ Completed {new Date(task.completedAt).toLocaleDateString()}
              {task.completedByLocation && ' (by location)'}
            </span>
          )}
        </div>

        {task.tags && task.tags.length > 0 && (
          <div className="task-tags">
            {task.tags.map((tag, index) => (
              <span key={index} className="tag">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>

      <div className="task-actions">
        {task.status !== 'completed' && (
          <>
            {task.isLocationBased && (
              <button
                className="btn btn-warning btn-sm"
                onClick={handleLocationComplete}
                disabled={loading}
                title="Complete by location"
              >
                📍 Check Location
              </button>
            )}
            
            <button
              className="btn btn-primary btn-sm"
              onClick={() => handleStatusChange('completed')}
              disabled={loading}
            >
              ✓ Complete
            </button>
            
            {task.status === 'pending' && (
              <button
                className="btn btn-warning btn-sm"
                onClick={() => handleStatusChange('in-progress')}
                disabled={loading}
              >
                ▶ Start
              </button>
            )}
          </>
        )}

        <button
          className="btn btn-secondary btn-sm"
          onClick={() => onEdit(task)}
          disabled={loading}
        >
          ✏️ Edit
        </button>

        <button
          className="btn btn-danger btn-sm"
          onClick={() => onDelete(task._id)}
          disabled={loading}
        >
          🗑️ Delete
        </button>
      </div>

      {loading && (
        <div className="task-loading">
          <div className="spinner-sm"></div>
        </div>
      )}
    </div>
  );
};

export default TaskItem;
