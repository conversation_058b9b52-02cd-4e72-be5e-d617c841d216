.tasks-page {
  padding: 2rem 0;
  min-height: calc(100vh - 80px);
}

.tasks-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.tasks-header h1 {
  color: #333;
  font-size: 2rem;
  margin: 0;
}

/* Filters */
.tasks-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-group {
  flex: 1;
  min-width: 200px;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modal {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

/* Task Form */
.task-form {
  padding: 2rem;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.form-header h2 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 2rem;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-row .form-group {
  flex: 1;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 0.5rem;
}

.location-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.location-section h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.tag-input {
  display: flex;
  gap: 0.5rem;
}

.tag-input .form-control {
  flex: 1;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.tag {
  background: #4CAF50;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  padding: 0;
  width: 1rem;
  height: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

/* Task Item */
.task-item {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
  position: relative;
}

.task-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.task-item.completed {
  background: #f8f9fa;
  opacity: 0.8;
}

.task-item.overdue {
  border-left: 4px solid #dc3545;
}

.task-content {
  margin-bottom: 1rem;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.task-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.3;
}

.task-item.completed .task-title {
  text-decoration: line-through;
  color: #666;
}

.task-badges {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.priority-badge,
.status-badge,
.location-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
}

.priority-badge.high { background: #fee; color: #dc3545; }
.priority-badge.medium { background: #fff3cd; color: #856404; }
.priority-badge.low { background: #d4edda; color: #155724; }

.status-badge.green { background: #d4edda; color: #155724; }
.status-badge.orange { background: #fff3cd; color: #856404; }
.status-badge.gray { background: #f8f9fa; color: #6c757d; }

.location-badge {
  background: #e3f2fd;
  color: #1976d2;
}

.task-description {
  color: #666;
  line-height: 1.5;
  margin: 0.5rem 0;
}

.task-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.875rem;
  color: #666;
  flex-wrap: wrap;
}

.task-date.overdue-text {
  color: #dc3545;
  font-weight: 500;
}

.location-info,
.completed-info {
  color: #4CAF50;
}

.task-tags {
  margin-top: 0.5rem;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.task-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}

/* Location Handler */
.location-handler {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.location-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #dc3545;
}

.status-dot.active {
  background: #28a745;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-weight: 500;
  color: #333;
}

.location-info {
  font-size: 0.875rem;
  color: #666;
}

.nearby-tasks {
  margin-top: 1rem;
}

.nearby-tasks h4 {
  color: #333;
  margin-bottom: 1rem;
}

.nearby-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.nearby-task {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #e8f5e8;
  border-radius: 8px;
  border: 1px solid #4CAF50;
}

.task-info {
  display: flex;
  flex-direction: column;
}

.location-name {
  font-size: 0.875rem;
  color: #666;
}

.location-prompt {
  margin-top: 1rem;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 2rem;
  padding: 1rem;
}

.pagination-info {
  color: #666;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  color: #333;
  margin-bottom: 0.5rem;
}

.empty-state p {
  margin-bottom: 2rem;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .tasks-page {
    padding: 1rem 0;
  }

  .tasks-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .tasks-filters {
    flex-direction: column;
  }

  .filter-group {
    min-width: auto;
  }

  .modal {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }

  .task-form {
    padding: 1.5rem;
  }

  .form-row {
    flex-direction: column;
  }

  .task-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .task-badges {
    justify-content: flex-start;
  }

  .task-actions {
    justify-content: flex-start;
  }

  .form-actions {
    flex-direction: column;
  }

  .nearby-task {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .location-status {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
