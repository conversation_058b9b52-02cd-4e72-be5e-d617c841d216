import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

const LocationTaskHandler = ({ tasks, onTaskUpdate }) => {
  const [watchId, setWatchId] = useState(null);
  const [currentPosition, setCurrentPosition] = useState(null);
  const [locationEnabled, setLocationEnabled] = useState(false);
  const [nearbyTasks, setNearbyTasks] = useState([]);

  useEffect(() => {
    // Check if there are any location-based tasks
    const locationTasks = tasks.filter(task => 
      task.isLocationBased && 
      task.status !== 'completed' &&
      task.location.coordinates.latitude &&
      task.location.coordinates.longitude
    );

    if (locationTasks.length > 0 && !locationEnabled) {
      startLocationTracking();
    } else if (locationTasks.length === 0 && locationEnabled) {
      stopLocationTracking();
    }

    return () => {
      if (watchId) {
        navigator.geolocation.clearWatch(watchId);
      }
    };
  }, [tasks]);

  useEffect(() => {
    if (currentPosition) {
      checkNearbyTasks();
    }
  }, [currentPosition, tasks]);

  const startLocationTracking = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    const options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 60000 // 1 minute
    };

    const id = navigator.geolocation.watchPosition(
      (position) => {
        setCurrentPosition({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy
        });
        setLocationEnabled(true);
      },
      (error) => {
        console.error('Geolocation error:', error);
        setLocationEnabled(false);
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            toast.error('Location access denied. Please enable location services.');
            break;
          case error.POSITION_UNAVAILABLE:
            toast.error('Location information is unavailable.');
            break;
          case error.TIMEOUT:
            toast.error('Location request timed out.');
            break;
          default:
            toast.error('An unknown error occurred while retrieving location.');
            break;
        }
      },
      options
    );

    setWatchId(id);
  };

  const stopLocationTracking = () => {
    if (watchId) {
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
    }
    setLocationEnabled(false);
    setCurrentPosition(null);
    setNearbyTasks([]);
  };

  const calculateDistance = (lat1, lon1, lat2, lon2) => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c; // Distance in meters
  };

  const checkNearbyTasks = () => {
    if (!currentPosition) return;

    const locationTasks = tasks.filter(task => 
      task.isLocationBased && 
      task.status !== 'completed' &&
      task.location.coordinates.latitude &&
      task.location.coordinates.longitude
    );

    const nearby = locationTasks.filter(task => {
      const distance = calculateDistance(
        currentPosition.latitude,
        currentPosition.longitude,
        task.location.coordinates.latitude,
        task.location.coordinates.longitude
      );

      return distance <= task.location.radius;
    });

    // Check for newly nearby tasks
    const newNearbyTasks = nearby.filter(task => 
      !nearbyTasks.some(nearbyTask => nearbyTask._id === task._id)
    );

    if (newNearbyTasks.length > 0) {
      newNearbyTasks.forEach(task => {
        toast.success(
          `📍 You're near "${task.title}"! Tap to complete.`,
          {
            onClick: () => handleLocationComplete(task._id),
            autoClose: 10000
          }
        );
      });
    }

    setNearbyTasks(nearby);
  };

  const handleLocationComplete = async (taskId) => {
    if (!currentPosition) {
      toast.error('Current location not available');
      return;
    }

    try {
      await axios.post(`/api/tasks/${taskId}/complete-by-location`, {
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude
      });
      toast.success('Task completed by location!');
      onTaskUpdate();
    } catch (error) {
      console.error('Location completion error:', error);
      toast.error(error.response?.data?.message || 'Failed to complete task by location');
    }
  };

  const locationBasedTasks = tasks.filter(task => 
    task.isLocationBased && task.status !== 'completed'
  );

  if (locationBasedTasks.length === 0) {
    return null;
  }

  return (
    <div className="location-handler">
      <div className="location-status">
        <div className="status-indicator">
          <span className={`status-dot ${locationEnabled ? 'active' : 'inactive'}`}></span>
          <span className="status-text">
            {locationEnabled ? 'Location tracking active' : 'Location tracking disabled'}
          </span>
        </div>

        {currentPosition && (
          <div className="location-info">
            <small>
              Accuracy: ±{Math.round(currentPosition.accuracy)}m
            </small>
          </div>
        )}
      </div>

      {nearbyTasks.length > 0 && (
        <div className="nearby-tasks">
          <h4>📍 Nearby Tasks</h4>
          <div className="nearby-tasks-list">
            {nearbyTasks.map(task => (
              <div key={task._id} className="nearby-task">
                <div className="task-info">
                  <strong>{task.title}</strong>
                  <span className="location-name">{task.location.name}</span>
                </div>
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => handleLocationComplete(task._id)}
                >
                  Complete Now
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {!locationEnabled && locationBasedTasks.length > 0 && (
        <div className="location-prompt">
          <div className="alert alert-warning">
            <strong>Location Services Required</strong>
            <p>You have {locationBasedTasks.length} location-based task{locationBasedTasks.length !== 1 ? 's' : ''}. Enable location services to automatically complete tasks when you arrive.</p>
            <button 
              className="btn btn-primary btn-sm"
              onClick={startLocationTracking}
            >
              Enable Location Tracking
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LocationTaskHandler;
