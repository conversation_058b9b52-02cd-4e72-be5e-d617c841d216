# 📚 API Documentation

Complete API reference for the MERN Stack Todo List Application.

## 🔐 Authentication

All protected routes require a JWT token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "efficiency": 0
  }
}
```

### Login User
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Get Current User
```http
GET /api/auth/me
Authorization: Bearer <token>
```

## ✅ Tasks API

### Get All Tasks
```http
GET /api/tasks?status=pending&priority=high&page=1&limit=10
Authorization: Bearer <token>
```

**Query Parameters:**
- `status`: pending, in-progress, completed
- `priority`: low, medium, high
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "tasks": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  }
}
```

### Create Task
```http
POST /api/tasks
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Complete project",
  "description": "Finish the MERN stack application",
  "priority": "high",
  "dueDate": "2024-12-31T23:59:59.000Z",
  "isLocationBased": true,
  "location": {
    "name": "Office",
    "address": "123 Main St, City, State",
    "coordinates": {
      "latitude": 40.7128,
      "longitude": -74.0060
    },
    "radius": 100
  },
  "tags": ["work", "urgent"]
}
```

### Update Task
```http
PUT /api/tasks/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "completed",
  "priority": "medium"
}
```

### Delete Task
```http
DELETE /api/tasks/:id
Authorization: Bearer <token>
```

### Complete Task by Location
```http
POST /api/tasks/:id/complete-by-location
Authorization: Bearer <token>
Content-Type: application/json

{
  "latitude": 40.7128,
  "longitude": -74.0060
}
```

## 👥 Peers API

### Get All Peers
```http
GET /api/peers
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "peers": [
    {
      "id": "peer_id",
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "efficiency": 85,
      "completedTasks": 42,
      "totalTasks": 50,
      "streak": 7,
      "addedAt": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

### Invite Peer
```http
POST /api/peers/invite
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### Remove Peer
```http
DELETE /api/peers/:id
Authorization: Bearer <token>
```

### Get Leaderboard
```http
GET /api/peers/leaderboard
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "leaderboard": [
    {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "efficiency": 92,
      "completedTasks": 46,
      "totalTasks": 50,
      "streak": 12,
      "isCurrentUser": true
    }
  ]
}
```

## 👤 Users API

### Get Dashboard Data
```http
GET /api/users/dashboard
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "dashboard": {
    "user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>",
      "efficiency": 85,
      "streak": 7
    },
    "taskStats": {
      "total": 50,
      "pending": 8,
      "inProgress": 5,
      "completed": 37,
      "overdue": 2
    },
    "recentTasks": [...],
    "efficiencyTrend": [
      {
        "date": "2024-01-15",
        "completed": 3
      }
    ]
  }
}
```

### Update Profile
```http
PUT /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "John Smith",
  "preferences": {
    "emailNotifications": true,
    "locationBasedTasks": false
  }
}
```

### Search Users
```http
GET /api/users/search?email=john
Authorization: Bearer <token>
```

## 📊 Data Models

### User Model
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  password: String, // hashed
  avatar: String,
  peers: [{
    user: ObjectId,
    addedAt: Date
  }],
  efficiency: {
    completedTasks: Number,
    totalTasks: Number,
    streak: Number,
    lastActiveDate: Date
  },
  preferences: {
    emailNotifications: Boolean,
    locationBasedTasks: Boolean
  },
  createdAt: Date,
  updatedAt: Date
}
```

### Task Model
```javascript
{
  _id: ObjectId,
  title: String,
  description: String,
  user: ObjectId,
  status: String, // pending, in-progress, completed
  priority: String, // low, medium, high
  dueDate: Date,
  location: {
    name: String,
    address: String,
    coordinates: {
      latitude: Number,
      longitude: Number
    },
    radius: Number
  },
  isLocationBased: Boolean,
  completedAt: Date,
  completedByLocation: Boolean,
  tags: [String],
  assignedPeers: [{
    user: ObjectId,
    assignedAt: Date
  }],
  reminders: [{
    type: String,
    scheduledFor: Date,
    sent: Boolean
  }],
  createdAt: Date,
  updatedAt: Date
}
```

## 🚨 Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Please provide a valid email"
    }
  ]
}
```

### Authentication Error (401)
```json
{
  "success": false,
  "message": "No token provided, authorization denied"
}
```

### Not Found Error (404)
```json
{
  "success": false,
  "message": "Task not found"
}
```

### Server Error (500)
```json
{
  "success": false,
  "message": "Server error"
}
```

## 📝 Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request / Validation Error
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

## 🔧 Rate Limiting

Currently not implemented but recommended for production:
- Authentication endpoints: 5 requests per minute
- General API: 100 requests per minute
- File uploads: 10 requests per minute

## 📱 WebSocket Events (Future Enhancement)

For real-time features:
- `task:created` - New task created
- `task:updated` - Task status changed
- `peer:invited` - New peer invitation
- `efficiency:updated` - Efficiency score changed

## 🧪 Testing the API

### Using cURL
```bash
# Register
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"name":"Test User","email":"<EMAIL>","password":"password123"}'

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Get tasks (replace TOKEN with actual JWT)
curl -X GET http://localhost:5000/api/tasks \
  -H "Authorization: Bearer TOKEN"
```

### Using Postman
1. Import the API endpoints
2. Set up environment variables for base URL and token
3. Use the authentication endpoints to get a token
4. Test all CRUD operations

---

**Happy API Testing! 🚀**
