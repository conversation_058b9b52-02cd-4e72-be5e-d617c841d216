{"name": "todo-backend", "version": "1.0.0", "description": "Backend API for MERN Todo List Application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.4", "node-cron": "^3.0.2", "axios": "^1.5.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["express", "mongodb", "api", "todo"], "author": "Your Name", "license": "MIT"}