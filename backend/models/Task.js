const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please provide a task title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'in-progress', 'completed'],
    default: 'pending'
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  dueDate: {
    type: Date
  },
  location: {
    name: {
      type: String,
      trim: true
    },
    address: {
      type: String,
      trim: true
    },
    coordinates: {
      latitude: {
        type: Number
      },
      longitude: {
        type: Number
      }
    },
    radius: {
      type: Number,
      default: 100 // meters
    }
  },
  isLocationBased: {
    type: Boolean,
    default: false
  },
  completedAt: {
    type: Date
  },
  completedByLocation: {
    type: <PERSON>olean,
    default: false
  },
  tags: [{
    type: String,
    trim: true
  }],
  assignedPeers: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    assignedAt: {
      type: Date,
      default: Date.now
    }
  }],
  reminders: [{
    type: {
      type: String,
      enum: ['email', 'push'],
      default: 'email'
    },
    scheduledFor: {
      type: Date,
      required: true
    },
    sent: {
      type: Boolean,
      default: false
    }
  }]
}, {
  timestamps: true
});

// Index for location-based queries
taskSchema.index({ 'location.coordinates': '2dsphere' });

// Index for user tasks
taskSchema.index({ user: 1, status: 1 });

// Method to check if task is overdue
taskSchema.methods.isOverdue = function() {
  if (!this.dueDate) return false;
  return this.dueDate < new Date() && this.status !== 'completed';
};

// Method to mark task as completed
taskSchema.methods.markCompleted = function(completedByLocation = false) {
  this.status = 'completed';
  this.completedAt = new Date();
  this.completedByLocation = completedByLocation;
  return this.save();
};

module.exports = mongoose.model('Task', taskSchema);
