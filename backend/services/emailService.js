const nodemailer = require('nodemailer');
const User = require('../models/User');
const Task = require('../models/Task');

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Send daily task notifications to all users
const sendDailyTaskNotifications = async () => {
  try {
    const users = await User.find({ 'preferences.emailNotifications': true });
    
    for (const user of users) {
      const pendingTasks = await Task.find({
        user: user._id,
        status: { $in: ['pending', 'in-progress'] }
      });

      const overdueTasks = pendingTasks.filter(task => task.isOverdue());
      const todayTasks = pendingTasks.filter(task => {
        if (!task.dueDate) return false;
        const today = new Date();
        const taskDate = new Date(task.dueDate);
        return taskDate.toDateString() === today.toDateString();
      });

      if (pendingTasks.length > 0) {
        await sendTaskReminderEmail(user, pendingTasks, overdueTasks, todayTasks);
      }
    }

    console.log('Daily task notifications sent successfully');
  } catch (error) {
    console.error('Error sending daily notifications:', error);
  }
};

// Send task reminder email to a specific user
const sendTaskReminderEmail = async (user, pendingTasks, overdueTasks, todayTasks) => {
  try {
    const transporter = createTransporter();

    const emailContent = generateTaskReminderHTML(user, pendingTasks, overdueTasks, todayTasks);

    const mailOptions = {
      from: `"Todo List App" <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: `Daily Task Reminder - ${todayTasks.length} tasks due today`,
      html: emailContent
    };

    await transporter.sendMail(mailOptions);
    console.log(`Task reminder sent to ${user.email}`);
  } catch (error) {
    console.error(`Error sending task reminder to ${user.email}:`, error);
  }
};

// Generate HTML content for task reminder email
const generateTaskReminderHTML = (user, pendingTasks, overdueTasks, todayTasks) => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background-color: #f9f9f9; }
            .task-section { margin: 20px 0; }
            .task-item { background: white; padding: 10px; margin: 5px 0; border-left: 4px solid #4CAF50; }
            .overdue { border-left-color: #f44336; }
            .today { border-left-color: #ff9800; }
            .stats { display: flex; justify-content: space-around; margin: 20px 0; }
            .stat { text-align: center; }
            .stat-number { font-size: 24px; font-weight: bold; color: #4CAF50; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Good Morning, ${user.name}!</h1>
                <p>Here's your daily task summary</p>
            </div>
            
            <div class="content">
                <div class="stats">
                    <div class="stat">
                        <div class="stat-number">${pendingTasks.length}</div>
                        <div>Pending Tasks</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${todayTasks.length}</div>
                        <div>Due Today</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${overdueTasks.length}</div>
                        <div>Overdue</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number">${user.getEfficiencyPercentage()}%</div>
                        <div>Efficiency</div>
                    </div>
                </div>

                ${overdueTasks.length > 0 ? `
                <div class="task-section">
                    <h3 style="color: #f44336;">⚠️ Overdue Tasks</h3>
                    ${overdueTasks.map(task => `
                        <div class="task-item overdue">
                            <strong>${task.title}</strong>
                            <br><small>Due: ${new Date(task.dueDate).toLocaleDateString()}</small>
                        </div>
                    `).join('')}
                </div>
                ` : ''}

                ${todayTasks.length > 0 ? `
                <div class="task-section">
                    <h3 style="color: #ff9800;">📅 Due Today</h3>
                    ${todayTasks.map(task => `
                        <div class="task-item today">
                            <strong>${task.title}</strong>
                            <br><small>Priority: ${task.priority.toUpperCase()}</small>
                        </div>
                    `).join('')}
                </div>
                ` : ''}

                <div class="task-section">
                    <h3>📝 All Pending Tasks</h3>
                    ${pendingTasks.slice(0, 5).map(task => `
                        <div class="task-item">
                            <strong>${task.title}</strong>
                            <br><small>Status: ${task.status} | Priority: ${task.priority}</small>
                            ${task.dueDate ? `<br><small>Due: ${new Date(task.dueDate).toLocaleDateString()}</small>` : ''}
                        </div>
                    `).join('')}
                    ${pendingTasks.length > 5 ? `<p><em>...and ${pendingTasks.length - 5} more tasks</em></p>` : ''}
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <a href="${process.env.CLIENT_URL}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">
                        Open Todo App
                    </a>
                </div>
            </div>
        </div>
    </body>
    </html>
  `;
};

// Send peer invitation email
const sendPeerInvitation = async (email, inviterName) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: `"Todo List App" <${process.env.EMAIL_USER}>`,
      to: email,
      subject: `${inviterName} added you as a peer on Todo List App`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background-color: #f9f9f9; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>You've been added as a peer!</h1>
                </div>
                <div class="content">
                    <p>Hi there!</p>
                    <p><strong>${inviterName}</strong> has added you as a peer on Todo List App. You can now track each other's productivity and efficiency!</p>
                    <p>Features you can now enjoy:</p>
                    <ul>
                        <li>View efficiency leaderboard</li>
                        <li>Track peer productivity</li>
                        <li>Collaborate on tasks</li>
                        <li>Motivate each other to stay productive</li>
                    </ul>
                    <div style="text-align: center; margin-top: 30px;">
                        <a href="${process.env.CLIENT_URL}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px;">
                            Open Todo App
                        </a>
                    </div>
                </div>
            </div>
        </body>
        </html>
      `
    };

    await transporter.sendMail(mailOptions);
    console.log(`Peer invitation sent to ${email}`);
  } catch (error) {
    console.error(`Error sending peer invitation to ${email}:`, error);
  }
};

module.exports = {
  sendDailyTaskNotifications,
  sendTaskReminderEmail,
  sendPeerInvitation
};
