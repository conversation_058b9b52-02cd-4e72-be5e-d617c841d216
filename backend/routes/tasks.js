const express = require('express');
const { body, validationResult } = require('express-validator');
const Task = require('../models/Task');
const User = require('../models/User');
const auth = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/tasks
// @desc    Get all tasks for the authenticated user
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { status, priority, page = 1, limit = 10 } = req.query;
    
    const filter = { user: req.user.id };
    if (status) filter.status = status;
    if (priority) filter.priority = priority;

    const tasks = await Task.find(filter)
      .populate('assignedPeers.user', 'name email')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Task.countDocuments(filter);

    res.json({
      success: true,
      tasks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/tasks
// @desc    Create a new task
// @access  Private
router.post('/', [
  auth,
  body('title').trim().isLength({ min: 1 }).withMessage('Title is required'),
  body('description').optional().trim(),
  body('priority').optional().isIn(['low', 'medium', 'high']),
  body('dueDate').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const taskData = {
      ...req.body,
      user: req.user.id
    };

    const task = new Task(taskData);
    await task.save();

    // Update user's total tasks count
    await User.findByIdAndUpdate(req.user.id, {
      $inc: { 'efficiency.totalTasks': 1 }
    });

    await task.populate('assignedPeers.user', 'name email');

    res.status(201).json({
      success: true,
      task
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/tasks/:id
// @desc    Get a specific task
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const task = await Task.findOne({
      _id: req.params.id,
      user: req.user.id
    }).populate('assignedPeers.user', 'name email');

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    res.json({
      success: true,
      task
    });
  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/tasks/:id
// @desc    Update a task
// @access  Private
router.put('/:id', [
  auth,
  body('title').optional().trim().isLength({ min: 1 }),
  body('description').optional().trim(),
  body('priority').optional().isIn(['low', 'medium', 'high']),
  body('status').optional().isIn(['pending', 'in-progress', 'completed']),
  body('dueDate').optional().isISO8601()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const task = await Task.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    const wasCompleted = task.status === 'completed';
    const isNowCompleted = req.body.status === 'completed';

    // Update task fields
    Object.keys(req.body).forEach(key => {
      if (key !== 'user') {
        task[key] = req.body[key];
      }
    });

    // Handle completion status change
    if (!wasCompleted && isNowCompleted) {
      task.completedAt = new Date();
      // Update user's completed tasks count
      await User.findByIdAndUpdate(req.user.id, {
        $inc: { 'efficiency.completedTasks': 1 }
      });
    } else if (wasCompleted && !isNowCompleted) {
      task.completedAt = null;
      // Decrease user's completed tasks count
      await User.findByIdAndUpdate(req.user.id, {
        $inc: { 'efficiency.completedTasks': -1 }
      });
    }

    await task.save();
    await task.populate('assignedPeers.user', 'name email');

    res.json({
      success: true,
      task
    });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/tasks/:id
// @desc    Delete a task
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const task = await Task.findOne({
      _id: req.params.id,
      user: req.user.id
    });

    if (!task) {
      return res.status(404).json({ message: 'Task not found' });
    }

    await Task.findByIdAndDelete(req.params.id);

    // Update user's task counts
    const updateData = { $inc: { 'efficiency.totalTasks': -1 } };
    if (task.status === 'completed') {
      updateData.$inc['efficiency.completedTasks'] = -1;
    }
    await User.findByIdAndUpdate(req.user.id, updateData);

    res.json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   POST /api/tasks/:id/complete-by-location
// @desc    Complete a task based on location
// @access  Private
router.post('/:id/complete-by-location', auth, async (req, res) => {
  try {
    const { latitude, longitude } = req.body;

    if (!latitude || !longitude) {
      return res.status(400).json({ message: 'Location coordinates are required' });
    }

    const task = await Task.findOne({
      _id: req.params.id,
      user: req.user.id,
      isLocationBased: true,
      status: { $ne: 'completed' }
    });

    if (!task) {
      return res.status(404).json({ message: 'Location-based task not found' });
    }

    // Calculate distance between current location and task location
    const distance = calculateDistance(
      latitude,
      longitude,
      task.location.coordinates.latitude,
      task.location.coordinates.longitude
    );

    if (distance <= task.location.radius) {
      await task.markCompleted(true);

      // Update user's completed tasks count
      await User.findByIdAndUpdate(req.user.id, {
        $inc: { 'efficiency.completedTasks': 1 }
      });

      res.json({
        success: true,
        message: 'Task completed by location!',
        task
      });
    } else {
      res.status(400).json({
        message: `You are ${Math.round(distance)}m away from the task location. Required distance: ${task.location.radius}m`
      });
    }
  } catch (error) {
    console.error('Location completion error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Helper function to calculate distance between two coordinates
function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = lat1 * Math.PI/180;
  const φ2 = lat2 * Math.PI/180;
  const Δφ = (lat2-lat1) * Math.PI/180;
  const Δλ = (lon2-lon1) * Math.PI/180;

  const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
          Math.cos(φ1) * Math.cos(φ2) *
          Math.sin(Δλ/2) * Math.sin(Δλ/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

  return R * c; // Distance in meters
}

module.exports = router;
