const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Task = require('../models/Task');
const auth = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/users/dashboard
// @desc    Get user dashboard data
// @access  Private
router.get('/dashboard', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    
    // Get task statistics
    const tasks = await Task.find({ user: req.user.id });
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    const inProgressTasks = tasks.filter(task => task.status === 'in-progress').length;
    const completedTasks = tasks.filter(task => task.status === 'completed').length;
    const overdueTasks = tasks.filter(task => task.isOverdue()).length;

    // Get recent tasks
    const recentTasks = await Task.find({ user: req.user.id })
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('assignedPeers.user', 'name email');

    // Calculate efficiency trends (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentCompletedTasks = await Task.find({
      user: req.user.id,
      status: 'completed',
      completedAt: { $gte: sevenDaysAgo }
    });

    const dailyCompletion = {};
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      dailyCompletion[dateStr] = 0;
    }

    recentCompletedTasks.forEach(task => {
      const dateStr = task.completedAt.toISOString().split('T')[0];
      if (dailyCompletion.hasOwnProperty(dateStr)) {
        dailyCompletion[dateStr]++;
      }
    });

    res.json({
      success: true,
      dashboard: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          efficiency: user.getEfficiencyPercentage(),
          streak: user.efficiency.streak
        },
        taskStats: {
          total: tasks.length,
          pending: pendingTasks,
          inProgress: inProgressTasks,
          completed: completedTasks,
          overdue: overdueTasks
        },
        recentTasks,
        efficiencyTrend: Object.entries(dailyCompletion).map(([date, count]) => ({
          date,
          completed: count
        }))
      }
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   PUT /api/users/profile
// @desc    Update user profile
// @access  Private
router.put('/profile', [
  auth,
  body('name').optional().trim().isLength({ min: 2 }),
  body('preferences.emailNotifications').optional().isBoolean(),
  body('preferences.locationBasedTasks').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const user = await User.findById(req.user.id);
    
    // Update allowed fields
    if (req.body.name) user.name = req.body.name;
    if (req.body.preferences) {
      user.preferences = { ...user.preferences, ...req.body.preferences };
    }

    await user.save();

    res.json({
      success: true,
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        preferences: user.preferences,
        efficiency: user.getEfficiencyPercentage()
      }
    });
  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/users/search
// @desc    Search users by email for peer invitation
// @access  Private
router.get('/search', auth, async (req, res) => {
  try {
    const { email } = req.query;
    
    if (!email) {
      return res.status(400).json({ message: 'Email query parameter is required' });
    }

    const users = await User.find({
      email: { $regex: email, $options: 'i' },
      _id: { $ne: req.user.id } // Exclude current user
    })
    .select('name email')
    .limit(10);

    res.json({
      success: true,
      users
    });
  } catch (error) {
    console.error('Search users error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
