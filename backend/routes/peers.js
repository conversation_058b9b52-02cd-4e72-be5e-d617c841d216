const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Task = require('../models/Task');
const auth = require('../middleware/auth');
const emailService = require('../services/emailService');

const router = express.Router();

// @route   POST /api/peers/invite
// @desc    Invite a peer by email
// @access  Private
router.post('/invite', [
  auth,
  body('email').isEmail().withMessage('Please provide a valid email')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { email } = req.body;

    // Check if the email belongs to an existing user
    const peerUser = await User.findOne({ email });
    if (!peerUser) {
      return res.status(404).json({ message: 'User with this email not found' });
    }

    // Check if already a peer
    const currentUser = await User.findById(req.user.id);
    const isAlreadyPeer = currentUser.peers.some(peer => 
      peer.user.toString() === peerUser._id.toString()
    );

    if (isAlreadyPeer) {
      return res.status(400).json({ message: 'User is already your peer' });
    }

    // Add peer to current user
    currentUser.peers.push({ user: peerUser._id });
    await currentUser.save();

    // Add current user as peer to the invited user
    peerUser.peers.push({ user: currentUser._id });
    await peerUser.save();

    // Send invitation email
    await emailService.sendPeerInvitation(peerUser.email, currentUser.name);

    res.json({
      success: true,
      message: 'Peer invitation sent successfully',
      peer: {
        id: peerUser._id,
        name: peerUser.name,
        email: peerUser.email,
        efficiency: peerUser.getEfficiencyPercentage()
      }
    });
  } catch (error) {
    console.error('Invite peer error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/peers
// @desc    Get all peers with their efficiency stats
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .populate('peers.user', 'name email efficiency');

    const peersWithStats = await Promise.all(
      user.peers.map(async (peer) => {
        const peerTasks = await Task.find({ user: peer.user._id });
        const completedTasks = peerTasks.filter(task => task.status === 'completed').length;
        const totalTasks = peerTasks.length;
        const efficiency = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        return {
          id: peer.user._id,
          name: peer.user.name,
          email: peer.user.email,
          efficiency,
          completedTasks,
          totalTasks,
          streak: peer.user.efficiency.streak,
          addedAt: peer.addedAt
        };
      })
    );

    res.json({
      success: true,
      peers: peersWithStats
    });
  } catch (error) {
    console.error('Get peers error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   DELETE /api/peers/:id
// @desc    Remove a peer
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    const peerId = req.params.id;

    // Remove peer from current user
    await User.findByIdAndUpdate(req.user.id, {
      $pull: { peers: { user: peerId } }
    });

    // Remove current user from peer's list
    await User.findByIdAndUpdate(peerId, {
      $pull: { peers: { user: req.user.id } }
    });

    res.json({
      success: true,
      message: 'Peer removed successfully'
    });
  } catch (error) {
    console.error('Remove peer error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET /api/peers/leaderboard
// @desc    Get efficiency leaderboard of peers
// @access  Private
router.get('/leaderboard', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id)
      .populate('peers.user', 'name email efficiency');

    // Include current user in leaderboard
    const allUsers = [
      {
        id: user._id,
        name: user.name,
        email: user.email,
        efficiency: user.getEfficiencyPercentage(),
        completedTasks: user.efficiency.completedTasks,
        totalTasks: user.efficiency.totalTasks,
        streak: user.efficiency.streak,
        isCurrentUser: true
      },
      ...user.peers.map(peer => ({
        id: peer.user._id,
        name: peer.user.name,
        email: peer.user.email,
        efficiency: peer.user.getEfficiencyPercentage(),
        completedTasks: peer.user.efficiency.completedTasks,
        totalTasks: peer.user.efficiency.totalTasks,
        streak: peer.user.efficiency.streak,
        isCurrentUser: false
      }))
    ];

    // Sort by efficiency percentage, then by streak
    const leaderboard = allUsers.sort((a, b) => {
      if (b.efficiency !== a.efficiency) {
        return b.efficiency - a.efficiency;
      }
      return b.streak - a.streak;
    });

    res.json({
      success: true,
      leaderboard
    });
  } catch (error) {
    console.error('Get leaderboard error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
