{"name": "todo-list-mern", "version": "1.0.0", "description": "A comprehensive MERN stack to-do list application with peer tracking and location-based features", "main": "server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "build": "cd frontend && npm run build", "install-server": "cd backend && npm install", "install-client": "cd frontend && npm install", "install-all": "npm run install-server && npm run install-client"}, "keywords": ["todo", "mern", "react", "nodejs", "mongodb", "express"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}