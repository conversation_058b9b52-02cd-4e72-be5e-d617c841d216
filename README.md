# MERN Stack Todo List Application

A comprehensive task management application built with the MERN stack (MongoDB, Express.js, React, Node.js) featuring peer productivity tracking, location-based task completion, and email notifications.

## 🚀 Features

### Core Features
- **User Authentication**: JWT-based secure authentication system
- **Task Management**: Create, edit, delete, and organize tasks with priorities and due dates
- **Real-time Dashboard**: View task statistics, efficiency metrics, and recent activities
- **Responsive Design**: Mobile-friendly interface that works on all devices

### Advanced Features
- **📍 Location-based Task Completion**: Automatically complete tasks when you reach specified locations using GPS
- **👥 Peer Productivity Tracking**: Invite friends and colleagues to track efficiency together
- **📧 Daily Email Notifications**: Receive daily task reminders via email using Nodemailer
- **🏆 Efficiency Leaderboard**: Compete with peers and track productivity metrics
- **🔥 Streak Tracking**: Maintain daily task completion streaks
- **📊 Analytics Dashboard**: Visualize productivity trends and statistics

## 🛠️ Tech Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication
- **Nodemailer** - Email notifications
- **Node-cron** - Scheduled tasks
- **Bcrypt** - Password hashing

### Frontend
- **React** - UI library
- **React Router** - Navigation
- **Axios** - HTTP client
- **React Toastify** - Notifications
- **CSS3** - Styling with responsive design

## 📋 Prerequisites

Before running this application, make sure you have the following installed:
- Node.js (v14 or higher)
- MongoDB (local installation or MongoDB Atlas)
- Git

## 🚀 Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd TO_DO_LIST
```

### 2. Install Dependencies
```bash
# Install root dependencies
npm install

# Install backend dependencies
npm run install-server

# Install frontend dependencies
npm run install-client
```

### 3. Environment Configuration

Create a `.env` file in the `backend` directory:
```env
PORT=5000
MONGODB_URI=mongodb://localhost:27017/todolist
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRE=30d

# Email Configuration (Gmail example)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password

# Google Maps API (optional for enhanced location features)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Frontend URL
CLIENT_URL=http://localhost:3000
```

### 4. Database Setup

Make sure MongoDB is running on your system:
```bash
# For local MongoDB
mongod

# Or use MongoDB Atlas cloud database
# Update MONGODB_URI in .env with your Atlas connection string
```

### 5. Email Configuration

For email notifications to work:
1. Use Gmail or another SMTP provider
2. For Gmail, enable 2-factor authentication and create an App Password
3. Update EMAIL_USER and EMAIL_PASS in your .env file

## 🏃‍♂️ Running the Application

### Development Mode
```bash
# Run both frontend and backend concurrently
npm run dev

# Or run them separately:
# Backend only
npm run server

# Frontend only
npm run client
```

### Production Mode
```bash
# Build frontend
npm run build

# Start backend server
npm start
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## 📱 Usage Guide

### Getting Started
1. **Register**: Create a new account with your email and password
2. **Login**: Sign in to access your dashboard
3. **Create Tasks**: Add your first task with title, description, and priority
4. **Set Location**: Enable location-based completion for tasks at specific places
5. **Invite Peers**: Add friends to track productivity together

### Location-Based Tasks
1. Create a task and enable "Location-based task"
2. Enter the location name and address
3. Set coordinates manually or use "Use Current Location"
4. Set completion radius (default: 100 meters)
5. The task will auto-complete when you reach the location

### Peer Productivity
1. Go to the Peers section
2. Click "Invite Peer" and enter their email
3. They'll receive an invitation email
4. Once they join, you can see each other's efficiency on the leaderboard

### Email Notifications
- Daily reminders are sent at 8 AM with pending tasks
- Overdue tasks are highlighted in red
- Task statistics and efficiency metrics included

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user

### Tasks
- `GET /api/tasks` - Get user tasks
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task
- `POST /api/tasks/:id/complete-by-location` - Complete task by location

### Peers
- `GET /api/peers` - Get user peers
- `POST /api/peers/invite` - Invite peer
- `DELETE /api/peers/:id` - Remove peer
- `GET /api/peers/leaderboard` - Get efficiency leaderboard

### Users
- `GET /api/users/dashboard` - Get dashboard data
- `PUT /api/users/profile` - Update user profile
- `GET /api/users/search` - Search users by email

## 🎨 Features in Detail

### Dashboard
- Task statistics (total, completed, pending, overdue)
- 7-day efficiency trend chart
- Recent tasks with quick actions
- Peer leaderboard preview

### Task Management
- Priority levels (High, Medium, Low)
- Due date tracking with overdue indicators
- Status management (Pending, In Progress, Completed)
- Tag system for organization
- Location-based completion

### Peer System
- Email-based invitations
- Real-time efficiency tracking
- Leaderboard with rankings
- Streak tracking and achievements

### Responsive Design
- Mobile-first approach
- Touch-friendly interface
- Optimized for all screen sizes
- Progressive Web App features

## 🔒 Security Features

- JWT token-based authentication
- Password hashing with bcrypt
- Input validation and sanitization
- CORS protection
- Rate limiting (can be added)
- Secure HTTP headers

## 🚀 Deployment

### Backend Deployment (Heroku/Railway/DigitalOcean)
1. Set environment variables
2. Update MONGODB_URI for production database
3. Deploy backend code
4. Update CLIENT_URL to production frontend URL

### Frontend Deployment (Netlify/Vercel)
1. Build the React app: `npm run build`
2. Deploy the build folder
3. Update API endpoints to production backend URL

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Commit changes: `git commit -m 'Add feature'`
4. Push to branch: `git push origin feature-name`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🐛 Troubleshooting

### Common Issues
1. **MongoDB Connection Error**: Ensure MongoDB is running and connection string is correct
2. **Email Not Sending**: Check SMTP credentials and app password
3. **Location Not Working**: Enable location permissions in browser
4. **CORS Errors**: Ensure backend CORS is configured for frontend URL

### Support
For issues and questions, please create an issue in the repository or contact the development team.

## 🎯 Future Enhancements

- Push notifications for mobile devices
- Task templates and recurring tasks
- Team workspaces and project management
- Advanced analytics and reporting
- Integration with calendar applications
- Voice commands for task creation
- Offline mode with sync capabilities

---

Built with ❤️ using the MERN stack
