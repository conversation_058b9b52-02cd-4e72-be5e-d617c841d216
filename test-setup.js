// Simple test to verify the project structure and basic functionality
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing MERN Stack Todo List Application Setup...\n');

// Test 1: Check project structure
console.log('📁 Checking project structure...');
const requiredFiles = [
  'package.json',
  'backend/package.json',
  'backend/server.js',
  'backend/models/User.js',
  'backend/models/Task.js',
  'backend/routes/auth.js',
  'backend/routes/tasks.js',
  'backend/routes/peers.js',
  'backend/routes/users.js',
  'backend/services/emailService.js',
  'backend/middleware/auth.js',
  'frontend/package.json',
  'frontend/src/App.js',
  'frontend/src/contexts/AuthContext.js',
  'README.md'
];

let missingFiles = [];
requiredFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    missingFiles.push(file);
  }
});

if (missingFiles.length === 0) {
  console.log('✅ All required files are present');
} else {
  console.log('❌ Missing files:', missingFiles);
}

// Test 2: Check package.json scripts
console.log('\n📦 Checking package.json scripts...');
try {
  const rootPackage = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const backendPackage = JSON.parse(fs.readFileSync('backend/package.json', 'utf8'));
  const frontendPackage = JSON.parse(fs.readFileSync('frontend/package.json', 'utf8'));

  const requiredRootScripts = ['dev', 'server', 'client'];
  const requiredBackendScripts = ['start', 'dev'];
  const requiredFrontendScripts = ['start', 'build'];

  console.log('Root scripts:', Object.keys(rootPackage.scripts || {}));
  console.log('Backend scripts:', Object.keys(backendPackage.scripts || {}));
  console.log('Frontend scripts:', Object.keys(frontendPackage.scripts || {}));
  
  console.log('✅ Package.json files are properly configured');
} catch (error) {
  console.log('❌ Error reading package.json files:', error.message);
}

// Test 3: Check environment configuration
console.log('\n🔧 Checking environment configuration...');
if (fs.existsSync('backend/.env.example')) {
  console.log('✅ Environment example file exists');
} else {
  console.log('❌ Missing backend/.env.example file');
}

if (fs.existsSync('backend/.env')) {
  console.log('✅ Environment file exists');
} else {
  console.log('⚠️  Environment file not found - you need to create backend/.env');
}

// Test 4: Check component structure
console.log('\n⚛️  Checking React component structure...');
const componentDirs = [
  'frontend/src/components/Auth',
  'frontend/src/components/Dashboard',
  'frontend/src/components/Tasks',
  'frontend/src/components/Peers',
  'frontend/src/components/Profile',
  'frontend/src/components/Layout'
];

let missingDirs = [];
componentDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    missingDirs.push(dir);
  }
});

if (missingDirs.length === 0) {
  console.log('✅ All component directories are present');
} else {
  console.log('❌ Missing component directories:', missingDirs);
}

// Test 5: Basic syntax check for main files
console.log('\n🔍 Performing basic syntax checks...');
try {
  // Check if main server file has basic structure
  const serverContent = fs.readFileSync('backend/server.js', 'utf8');
  if (serverContent.includes('express') && serverContent.includes('mongoose')) {
    console.log('✅ Backend server file looks good');
  } else {
    console.log('⚠️  Backend server file might be incomplete');
  }

  // Check if main App component has basic structure
  const appContent = fs.readFileSync('frontend/src/App.js', 'utf8');
  if (appContent.includes('React') && appContent.includes('Router')) {
    console.log('✅ Frontend App component looks good');
  } else {
    console.log('⚠️  Frontend App component might be incomplete');
  }
} catch (error) {
  console.log('❌ Error checking file contents:', error.message);
}

console.log('\n🎉 Setup test completed!');
console.log('\n📋 Next steps to run the application:');
console.log('1. Install dependencies: npm run install-all');
console.log('2. Create backend/.env file with your configuration');
console.log('3. Start MongoDB service');
console.log('4. Run the application: npm run dev');
console.log('\n🌐 The app will be available at http://localhost:3000');
