#!/bin/bash

echo "🚀 Setting up MERN Stack Todo List Application..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if MongoDB is installed
if ! command -v mongod &> /dev/null; then
    echo "⚠️  MongoDB is not installed locally. You can use MongoDB Atlas instead."
    echo "   Make sure to update MONGODB_URI in backend/.env"
fi

echo "📦 Installing dependencies..."

# Install root dependencies
echo "Installing root dependencies..."
npm install

# Install backend dependencies
echo "Installing backend dependencies..."
cd backend && npm install && cd ..

# Install frontend dependencies
echo "Installing frontend dependencies..."
cd frontend && npm install && cd ..

# Create .env file if it doesn't exist
if [ ! -f backend/.env ]; then
    echo "📝 Creating environment configuration file..."
    cp backend/.env.example backend/.env
    echo "✅ Created backend/.env file. Please update it with your configuration."
else
    echo "✅ Environment file already exists."
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Update backend/.env with your MongoDB URI and email credentials"
echo "2. Start MongoDB (if using local installation): mongod"
echo "3. Run the application: npm run dev"
echo ""
echo "🌐 The application will be available at:"
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:5000"
echo ""
echo "📚 For detailed instructions, see README.md"
