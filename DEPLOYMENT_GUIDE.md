# 🚀 Deployment Guide

Complete guide for deploying the MERN Stack Todo List Application to production.

## 🌐 Deployment Options

### Backend Deployment Platforms
- **Heroku** - Easy deployment with Git integration
- **Railway** - Modern platform with automatic deployments
- **DigitalOcean App Platform** - Scalable with managed databases
- **AWS EC2** - Full control with custom configuration
- **Google Cloud Platform** - Enterprise-grade infrastructure

### Frontend Deployment Platforms
- **Netlify** - Automatic deployments from Git
- **Vercel** - Optimized for React applications
- **GitHub Pages** - Free hosting for static sites
- **AWS S3 + CloudFront** - Scalable static hosting
- **Firebase Hosting** - Google's hosting platform

### Database Options
- **MongoDB Atlas** - Managed MongoDB cloud service
- **AWS DocumentDB** - MongoDB-compatible database
- **Local MongoDB** - Self-hosted on VPS

## 🔧 Pre-Deployment Checklist

### Environment Configuration
- [ ] Update JWT_SECRET with strong production key
- [ ] Configure production MongoDB URI
- [ ] Set up email service credentials
- [ ] Update CLIENT_URL to production frontend URL
- [ ] Configure CORS for production domains
- [ ] Set NODE_ENV=production

### Security Hardening
- [ ] Enable HTTPS/SSL certificates
- [ ] Configure rate limiting
- [ ] Set up proper CORS policies
- [ ] Implement request logging
- [ ] Configure security headers
- [ ] Set up monitoring and alerts

### Performance Optimization
- [ ] Enable gzip compression
- [ ] Optimize database queries
- [ ] Set up CDN for static assets
- [ ] Configure caching strategies
- [ ] Minimize bundle sizes

## 🚀 Backend Deployment

### Option 1: Heroku Deployment

#### 1. Prepare for Heroku
```bash
# Install Heroku CLI
npm install -g heroku

# Login to Heroku
heroku login

# Create Heroku app
heroku create your-todo-app-backend
```

#### 2. Configure Environment Variables
```bash
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your_super_secure_jwt_secret_here
heroku config:set MONGODB_URI=your_mongodb_atlas_connection_string
heroku config:set EMAIL_HOST=smtp.gmail.com
heroku config:set EMAIL_PORT=587
heroku config:set EMAIL_USER=<EMAIL>
heroku config:set EMAIL_PASS=your_app_password
heroku config:set CLIENT_URL=https://your-frontend-domain.com
```

#### 3. Deploy Backend
```bash
# Add Heroku remote
git remote add heroku https://git.heroku.com/your-todo-app-backend.git

# Deploy backend only
git subtree push --prefix backend heroku main

# Or create separate backend repository
cd backend
git init
git add .
git commit -m "Initial backend commit"
git remote add heroku https://git.heroku.com/your-todo-app-backend.git
git push heroku main
```

### Option 2: Railway Deployment

#### 1. Connect Repository
- Visit [railway.app](https://railway.app)
- Connect your GitHub repository
- Select the backend folder

#### 2. Configure Environment Variables
Add the same environment variables as Heroku through Railway's dashboard.

#### 3. Deploy
Railway automatically deploys on Git push.

### Option 3: DigitalOcean App Platform

#### 1. Create App
- Visit DigitalOcean App Platform
- Connect your repository
- Configure build settings

#### 2. App Spec Configuration
```yaml
name: todo-backend
services:
- name: api
  source_dir: /backend
  github:
    repo: your-username/your-repo
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: JWT_SECRET
    value: your_jwt_secret
    type: SECRET
  - key: MONGODB_URI
    value: your_mongodb_uri
    type: SECRET
```

## 🎨 Frontend Deployment

### Option 1: Netlify Deployment

#### 1. Build Configuration
Create `netlify.toml` in frontend directory:
```toml
[build]
  base = "frontend/"
  publish = "build/"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[build.environment]
  REACT_APP_API_URL = "https://your-backend-url.herokuapp.com"
```

#### 2. Deploy
- Connect GitHub repository to Netlify
- Set build command: `npm run build`
- Set publish directory: `build`
- Deploy automatically on Git push

### Option 2: Vercel Deployment

#### 1. Install Vercel CLI
```bash
npm install -g vercel
```

#### 2. Deploy
```bash
cd frontend
vercel --prod
```

#### 3. Configure Environment Variables
Add environment variables in Vercel dashboard:
```
REACT_APP_API_URL=https://your-backend-url.com
```

### Option 3: Manual Build and Upload

#### 1. Build Frontend
```bash
cd frontend
npm run build
```

#### 2. Upload to Hosting
Upload the `build` folder contents to your hosting provider.

## 🗄️ Database Setup

### MongoDB Atlas (Recommended)

#### 1. Create Cluster
- Visit [MongoDB Atlas](https://www.mongodb.com/atlas)
- Create free cluster
- Choose cloud provider and region

#### 2. Configure Security
- Create database user
- Add IP addresses to whitelist (0.0.0.0/0 for all IPs)
- Get connection string

#### 3. Update Environment Variables
```bash
MONGODB_URI=mongodb+srv://username:<EMAIL>/todolist?retryWrites=true&w=majority
```

## 📧 Email Service Configuration

### Gmail Setup
1. Enable 2-Factor Authentication
2. Generate App Password
3. Use app password in EMAIL_PASS

### SendGrid Setup (Alternative)
```bash
npm install @sendgrid/mail
```

Update email service to use SendGrid API.

## 🔒 SSL/HTTPS Configuration

### Automatic SSL (Recommended)
Most platforms (Netlify, Vercel, Heroku) provide automatic SSL.

### Custom SSL
For custom domains, configure SSL certificates:
- Let's Encrypt (free)
- CloudFlare SSL
- Custom SSL certificates

## 📊 Monitoring and Analytics

### Application Monitoring
```bash
# Add monitoring service
npm install newrelic
# or
npm install @sentry/node
```

### Error Tracking
Configure error tracking in production:
```javascript
// In server.js
if (process.env.NODE_ENV === 'production') {
  const Sentry = require('@sentry/node');
  Sentry.init({ dsn: process.env.SENTRY_DSN });
}
```

## 🔄 CI/CD Pipeline

### GitHub Actions Example
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{secrets.HEROKU_API_KEY}}
        heroku_app_name: "your-backend-app"
        heroku_email: "<EMAIL>"
        appdir: "backend"

  deploy-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Deploy to Netlify
      run: |
        cd frontend
        npm install
        npm run build
        npx netlify-cli deploy --prod --dir=build
      env:
        NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
        NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}
```

## 🧪 Production Testing

### Health Check Endpoints
Add health check routes:
```javascript
// In server.js
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});
```

### Load Testing
```bash
# Install artillery for load testing
npm install -g artillery

# Create load test
artillery quick --count 10 --num 5 https://your-api-url.com/health
```

## 🔧 Troubleshooting

### Common Deployment Issues

#### 1. Build Failures
- Check Node.js version compatibility
- Verify all dependencies are in package.json
- Check for environment-specific code

#### 2. Database Connection Issues
- Verify MongoDB URI format
- Check IP whitelist settings
- Ensure database user has proper permissions

#### 3. CORS Errors
- Update CORS configuration for production domains
- Verify CLIENT_URL environment variable
- Check preflight request handling

#### 4. Email Service Issues
- Verify SMTP credentials
- Check firewall/security settings
- Test email service separately

### Performance Issues
- Enable compression middleware
- Optimize database queries
- Implement caching strategies
- Use CDN for static assets

## 📈 Scaling Considerations

### Horizontal Scaling
- Load balancers
- Multiple server instances
- Database clustering
- CDN implementation

### Vertical Scaling
- Increase server resources
- Optimize application code
- Database performance tuning
- Caching strategies

## 🎯 Post-Deployment Checklist

- [ ] Verify all features work in production
- [ ] Test user registration and login
- [ ] Confirm email notifications work
- [ ] Test location-based features
- [ ] Verify peer invitation system
- [ ] Check mobile responsiveness
- [ ] Test performance under load
- [ ] Set up monitoring and alerts
- [ ] Configure backup strategies
- [ ] Document production URLs and credentials

---

**Your MERN Stack Todo List is now ready for production! 🎉**

Remember to monitor your application regularly and keep dependencies updated for security and performance.
