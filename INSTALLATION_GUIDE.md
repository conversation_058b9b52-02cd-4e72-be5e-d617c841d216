# 🚀 Complete Installation Guide

## Prerequisites

Before you begin, ensure you have the following installed on your system:

### Required Software
1. **Node.js** (v16 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version` and `npm --version`

2. **MongoDB** (Choose one option):
   - **Option A - Local MongoDB**: Download from https://www.mongodb.com/try/download/community
   - **Option B - MongoDB Atlas** (Cloud): Create free account at https://www.mongodb.com/atlas

3. **Git** (for cloning the repository)
   - Download from: https://git-scm.com/

## 📥 Installation Steps

### Step 1: Clone and Setup
```bash
# Clone the repository (or download as ZIP)
git clone <your-repo-url>
cd TO_DO_LIST

# Install root dependencies
npm install

# Install backend dependencies
cd backend
npm install
cd ..

# Install frontend dependencies
cd frontend
npm install
cd ..
```

### Step 2: Database Setup

#### Option A: Local MongoDB
```bash
# Start MongoDB service
# On Windows: Start MongoDB service from Services
# On macOS: brew services start mongodb/brew/mongodb-community
# On Linux: sudo systemctl start mongod

# Verify MongoDB is running
mongo --eval "db.adminCommand('ismaster')"
```

#### Option B: MongoDB Atlas (Cloud)
1. Create account at https://www.mongodb.com/atlas
2. Create a new cluster (free tier available)
3. Get connection string from "Connect" button
4. Update `MONGODB_URI` in `backend/.env`

### Step 3: Environment Configuration

The `backend/.env` file has been created with default values. Update these settings:

```env
# Database (update if using MongoDB Atlas)
MONGODB_URI=mongodb://localhost:27017/todolist

# Security (IMPORTANT: Change in production)
JWT_SECRET=your_super_secret_jwt_key_change_this_in_production_2024

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

#### Email Setup (Optional but Recommended)
For Gmail:
1. Enable 2-Factor Authentication
2. Generate App Password: Google Account → Security → App passwords
3. Use the app password in `EMAIL_PASS`

### Step 4: Run the Application

```bash
# Start both frontend and backend
npm run dev

# Or run separately:
# Backend only: npm run server
# Frontend only: npm run client
```

## 🌐 Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000

## 🧪 Testing the Setup

Run the test script to verify everything is working:
```bash
node test-setup.js
```

## 📱 First Time Usage

1. **Register**: Create your account at http://localhost:3000/register
2. **Login**: Sign in with your credentials
3. **Create Tasks**: Add your first task from the dashboard
4. **Enable Location**: Allow location access for location-based tasks
5. **Invite Peers**: Add friends to track productivity together

## 🔧 Troubleshooting

### Common Issues

#### 1. MongoDB Connection Error
```
Error: connect ECONNREFUSED 127.0.0.1:27017
```
**Solution**: 
- Ensure MongoDB is running
- Check MONGODB_URI in .env file
- For Atlas, verify connection string and network access

#### 2. Port Already in Use
```
Error: listen EADDRINUSE :::5000
```
**Solution**: 
- Change PORT in backend/.env
- Or kill process using the port: `lsof -ti:5000 | xargs kill -9`

#### 3. Email Not Sending
**Solution**: 
- Verify EMAIL_USER and EMAIL_PASS in .env
- For Gmail, use App Password, not regular password
- Check SMTP settings for other providers

#### 4. Location Features Not Working
**Solution**: 
- Allow location access in browser
- Use HTTPS in production for location API
- Check browser console for errors

#### 5. Frontend Build Issues
```
npm ERR! peer dep missing
```
**Solution**: 
```bash
cd frontend
npm install --legacy-peer-deps
```

### Development Tips

1. **Hot Reload**: Both frontend and backend support hot reload
2. **Database GUI**: Use MongoDB Compass for database visualization
3. **API Testing**: Use Postman or Thunder Client for API testing
4. **Debugging**: Check browser console and terminal for errors

## 🚀 Production Deployment

### Backend (Heroku/Railway/DigitalOcean)
1. Set environment variables in hosting platform
2. Update MONGODB_URI to production database
3. Update CLIENT_URL to production frontend URL
4. Deploy backend code

### Frontend (Netlify/Vercel)
1. Build: `cd frontend && npm run build`
2. Deploy build folder
3. Set environment variables if needed

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Review console errors
3. Verify all dependencies are installed
4. Ensure MongoDB is running
5. Check environment variables

## 🎯 Next Steps

Once everything is running:
1. Explore the dashboard and create tasks
2. Try location-based task completion
3. Invite peers to test collaboration features
4. Set up email notifications
5. Customize the application for your needs

---

**Happy Task Managing! 🎉**
